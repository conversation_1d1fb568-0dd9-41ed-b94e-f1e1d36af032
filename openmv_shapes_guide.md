# OpenMV形状识别函数使用指南

## 🚀 快速开始

### 1. 基本设置
```python
import sensor
import image
import time
from openmv_simple_shapes import *

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)

clock = time.clock()
```

### 2. 基本使用
```python
while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 检测并绘制矩形
    rectangles = find_and_draw_rectangles(img)
    
    # 显示FPS
    img.draw_string(10, 220, f"FPS: {clock.fps():.1f}", color=(255, 255, 0))
```

## 📋 函数速查表

### 单一形状检测函数

| 函数名 | 功能 | 返回值 | 绘制颜色 |
|--------|------|--------|----------|
| `find_and_draw_rectangles(img)` | 检测矩形并画框 | 矩形列表 | 绿色 |
| `find_and_draw_circles(img)` | 检测圆形并画框 | 圆形列表 | 红色 |
| `find_and_draw_lines(img)` | 检测直线并绘制 | 直线列表 | 蓝色 |
| `find_and_draw_blobs(img)` | 检测色块并画框 | 色块列表 | 黄色 |

### 组合检测函数

| 函数名 | 功能 | 参数 |
|--------|------|------|
| `detect_shapes(img, shapes)` | 检测多种形状 | shapes=['rectangle', 'circle'] |
| `auto_detect_all_shapes(img)` | 自动检测所有形状 | 无 |
| `draw_detection_stats(img, results)` | 绘制统计信息 | results为检测结果字典 |

## 🎯 使用示例

### 示例1: 检测矩形
```python
while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 检测矩形
    rectangles = find_and_draw_rectangles(img)
    
    # 显示检测数量
    if rectangles:
        img.draw_string(10, 10, f"矩形: {len(rectangles)}", color=(255, 255, 255))
```

### 示例2: 检测圆形
```python
while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 检测圆形
    circles = find_and_draw_circles(img)
    
    # 显示每个圆的半径
    for i, circle in enumerate(circles):
        print(f"圆形 {i+1}: 半径 = {circle.r()}")
```

### 示例3: 检测多种形状
```python
while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 同时检测矩形和圆形
    results = detect_shapes(img, shapes=['rectangle', 'circle'])
    
    # 显示统计信息
    draw_detection_stats(img, results)
```

### 示例4: 自动检测所有形状
```python
while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 自动检测所有形状并显示统计
    results = auto_detect_all_shapes(img)
    
    # 打印检测结果
    for shape_type, shapes in results.items():
        if shapes:
            print(f"检测到 {len(shapes)} 个 {shape_type}")
```

## 🔧 参数调整

### 调整检测阈值
```python
# 矩形检测 - 调整面积阈值
rectangles = find_and_draw_rectangles(img, threshold=5000)  # 默认10000

# 圆形检测 - 调整检测阈值
circles = find_and_draw_circles(img, threshold=1000)  # 默认2000

# 直线检测 - 调整强度阈值
lines = find_and_draw_lines(img, threshold=500)  # 默认1000
```

### 调整绘制颜色
```python
# 使用自定义颜色
rectangles = find_and_draw_rectangles(img, color=(255, 0, 255))  # 紫色
circles = find_and_draw_circles(img, color=(0, 255, 255))        # 青色
lines = find_and_draw_lines(img, color=(255, 255, 0))            # 黄色
```

### 色块检测阈值调整
```python
# 自定义颜色阈值 - LAB颜色空间
red_thresholds = [(30, 100, 15, 127, 15, 127)]      # 红色
green_thresholds = [(30, 100, -64, -8, -32, 32)]    # 绿色
blue_thresholds = [(0, 30, 0, 64, -128, 0)]         # 蓝色

blobs = find_and_draw_blobs(img, thresholds=red_thresholds)
```

## 📊 检测结果处理

### 获取形状属性
```python
# 矩形属性
for rect in rectangles:
    x, y = rect.x(), rect.y()           # 左上角坐标
    w, h = rect.w(), rect.h()           # 宽度和高度
    area = w * h                        # 面积
    corners = rect.corners()            # 四个角点

# 圆形属性
for circle in circles:
    x, y = circle.x(), circle.y()       # 圆心坐标
    r = circle.r()                      # 半径
    area = 3.14159 * r * r              # 面积

# 直线属性
for line in lines:
    x1, y1 = line.x1(), line.y1()       # 起点
    x2, y2 = line.x2(), line.y2()       # 终点
    theta = line.theta()                # 角度
    rho = line.rho()                    # 距离

# 色块属性
for blob in blobs:
    x, y = blob.x(), blob.y()           # 边界框左上角
    w, h = blob.w(), blob.h()           # 边界框尺寸
    cx, cy = blob.cx(), blob.cy()       # 中心点
    area = blob.pixels()                # 像素数量
```

### 形状过滤
```python
# 过滤大矩形
large_rectangles = [rect for rect in rectangles if rect.w() > 50 and rect.h() > 50]

# 过滤大圆形
large_circles = [circle for circle in circles if circle.r() > 20]

# 过滤水平线
horizontal_lines = [line for line in lines if abs(line.theta()) < 10 or abs(line.theta()) > 170]
```

## ⚡ 性能优化

### 1. 降低分辨率
```python
sensor.set_framesize(sensor.QQVGA)  # 160x120，更快的处理速度
```

### 2. 设置ROI（感兴趣区域）
```python
# 只在图像中心区域检测
roi = (80, 60, 160, 120)  # (x, y, w, h)
rectangles = img.find_rects(roi=roi)
```

### 3. 跳帧处理
```python
frame_count = 0
while True:
    clock.tick()
    img = sensor.snapshot()
    
    frame_count += 1
    if frame_count % 3 == 0:  # 每3帧处理一次
        rectangles = find_and_draw_rectangles(img)
```

## 🐛 常见问题

### Q: 检测不到形状？
A: 尝试调整阈值参数，或检查光照条件

### Q: 检测到太多噪点？
A: 提高threshold值，或添加形状大小过滤

### Q: 圆形检测不准确？
A: 调整r_min和r_max参数，确保在预期半径范围内

### Q: 直线检测效果差？
A: 确保图像中有清晰的边缘，可能需要预处理

## 📁 文件说明

- **`openmv_simple_shapes.py`** - 简化的形状检测函数库
- **`openmv_shape_detection.py`** - 完整的形状检测类
- **`openmv_complete_example.py`** - 完整使用示例
- **`openmv_main_example.py`** - 主程序示例

## 🔗 扩展功能

### 添加形状计数
```python
def count_shapes(img):
    results = auto_detect_all_shapes(img)
    total = sum(len(shapes) for shapes in results.values() if shapes)
    img.draw_string(10, 10, f"总形状: {total}", color=(255, 255, 255))
    return total
```

### 形状分类
```python
def classify_rectangles(rectangles):
    squares = [r for r in rectangles if abs(r.w() - r.h()) < 10]
    rectangles_only = [r for r in rectangles if abs(r.w() - r.h()) >= 10]
    return squares, rectangles_only
```
