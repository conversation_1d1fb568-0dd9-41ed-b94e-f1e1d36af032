# openmv_main_example.py - OpenMV形状识别主程序示例
"""
OpenMV形状识别主程序
演示如何使用形状检测函数识别并绘制各种形状
"""

import sensor
import image
import time
import lcd

# 导入我们的形状检测函数
from openmv_shape_detection import *

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)
sensor.set_auto_gain(False)
sensor.set_auto_whitebal(False)

# 初始化LCD显示屏（如果有）
try:
    lcd.init()
    lcd_available = True
    print("LCD初始化成功")
except:
    lcd_available = False
    print("LCD不可用，使用IDE显示")

# 创建形状检测器
shape_detector = OpenMVShapeDetector()

# 检测模式设置
detection_modes = [
    {"name": "矩形检测", "shapes": ["rectangle"]},
    {"name": "圆形检测", "shapes": ["circle"]},
    {"name": "直线检测", "shapes": ["line"]},
    {"name": "色块检测", "shapes": ["blob"]},
    {"name": "矩形+圆形", "shapes": ["rectangle", "circle"]},
    {"name": "全部形状", "shapes": ["rectangle", "circle", "line"]},
]

current_mode = 0
last_switch_time = time.ticks_ms()
switch_interval = 3000  # 3秒切换一次模式

print("OpenMV形状识别程序启动")
print("程序将自动切换检测模式")

clock = time.clock()

while True:
    clock.tick()
    
    # 获取图像
    img = sensor.snapshot()
    
    # 自动切换检测模式
    current_time = time.ticks_ms()
    if current_time - last_switch_time > switch_interval:
        current_mode = (current_mode + 1) % len(detection_modes)
        last_switch_time = current_time
        print(f"切换到: {detection_modes[current_mode]['name']}")
    
    # 当前检测模式
    mode = detection_modes[current_mode]
    
    # 执行形状检测
    if mode['shapes'] == ["rectangle"]:
        # 矩形检测
        rectangles = detect_and_draw_rectangles(img)
        
        # 显示检测信息
        if rectangles:
            img.draw_string(10, 10, f"Rectangles: {len(rectangles)}", 
                          color=(255, 255, 255), scale=2)
            for i, rect in enumerate(rectangles):
                info = f"R{i+1}: {rect.w()}x{rect.h()}"
                img.draw_string(10, 35 + i*20, info, color=(0, 255, 0), scale=1)
    
    elif mode['shapes'] == ["circle"]:
        # 圆形检测
        circles = detect_and_draw_circles(img)
        
        # 显示检测信息
        if circles:
            img.draw_string(10, 10, f"Circles: {len(circles)}", 
                          color=(255, 255, 255), scale=2)
            for i, circle in enumerate(circles):
                info = f"C{i+1}: r={circle.r()}"
                img.draw_string(10, 35 + i*20, info, color=(255, 0, 0), scale=1)
    
    elif mode['shapes'] == ["line"]:
        # 直线检测
        lines = detect_and_draw_lines(img)
        
        # 显示检测信息
        if lines:
            img.draw_string(10, 10, f"Lines: {len(lines)}", 
                          color=(255, 255, 255), scale=2)
            for i, line in enumerate(lines):
                info = f"L{i+1}: {line.theta()}°"
                img.draw_string(10, 35 + i*20, info, color=(0, 0, 255), scale=1)
    
    elif mode['shapes'] == ["blob"]:
        # 色块检测
        blobs = detect_and_draw_blobs(img)
        
        # 显示检测信息
        if blobs:
            img.draw_string(10, 10, f"Blobs: {len(blobs)}", 
                          color=(255, 255, 255), scale=2)
            for i, blob in enumerate(blobs):
                info = f"B{i+1}: {blob.w()}x{blob.h()}"
                img.draw_string(10, 35 + i*20, info, color=(255, 255, 0), scale=1)
    
    else:
        # 多种形状检测
        results = detect_all_shapes(img, shapes=mode['shapes'])
        
        # 显示统计信息
        y_pos = 10
        for shape_type, shapes in results.items():
            if shapes:
                count_text = f"{shape_type}: {len(shapes)}"
                img.draw_string(10, y_pos, count_text, color=(255, 255, 255), scale=1)
                y_pos += 20
    
    # 显示当前模式和FPS
    img.draw_string(10, img.height() - 40, f"Mode: {mode['name']}", 
                   color=(255, 255, 0), scale=1)
    img.draw_string(10, img.height() - 20, f"FPS: {clock.fps():.1f}", 
                   color=(255, 255, 0), scale=1)
    
    # 显示到LCD或IDE
    if lcd_available:
        lcd.display(img)
    
    # 打印检测结果到控制台
    if current_time % 1000 < 50:  # 每秒打印一次
        print(f"[{mode['name']}] FPS: {clock.fps():.1f}")

# 注意：这个while循环会一直运行，按Ctrl+C停止
