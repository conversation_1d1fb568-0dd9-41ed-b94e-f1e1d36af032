# flip_utils.py - K230图像翻转工具函数
"""
简洁的K230图像翻转工具库
提供最常用的翻转函数，可直接导入使用
"""

# ==================== 基础翻转函数 ====================

def mirror_image(img):
    """水平翻转（左右镜像）- 最常用"""
    return img.mirror()

def flip_image(img):
    """垂直翻转（上下翻转）"""
    return img.flip()

def rotate_90_clockwise(img):
    """顺时针旋转90度"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=90)

def rotate_90_counterclockwise(img):
    """逆时针旋转90度"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=270)

def rotate_180_degrees(img):
    """旋转180度（倒置）"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=180)

# ==================== 快捷函数（简短命名） ====================

def mirror(img):
    """水平翻转 - 简短版本"""
    return img.mirror()

def flip(img):
    """垂直翻转 - 简短版本"""
    return img.flip()

def rot90(img):
    """顺时针90度 - 简短版本"""
    return img.rotation_corr(z_rotation=90)

def rot180(img):
    """180度旋转 - 简短版本"""
    return img.rotation_corr(z_rotation=180)

def rot270(img):
    """逆时针90度 - 简短版本"""
    return img.rotation_corr(z_rotation=270)

# ==================== 常用场景函数 ====================

def selfie_mode(img):
    """自拍模式（前置摄像头效果）"""
    return img.mirror()

def fix_upside_down(img):
    """修正倒装摄像头"""
    return img.rotation_corr(z_rotation=180)

def fix_left_mounted(img):
    """修正左侧安装的摄像头"""
    return img.rotation_corr(z_rotation=270)

def fix_right_mounted(img):
    """修正右侧安装的摄像头"""
    return img.rotation_corr(z_rotation=90)

# ==================== 一键变换函数 ====================

def quick_transform(img, mode="normal"):
    """
    一键变换函数
    mode可选值:
    - "normal": 无变换
    - "mirror": 水平翻转
    - "flip": 垂直翻转
    - "rot90": 顺时针90度
    - "rot180": 180度
    - "rot270": 逆时针90度
    - "selfie": 自拍模式
    - "upside": 倒装修正
    """
    if mode == "mirror":
        return img.mirror()
    elif mode == "flip":
        return img.flip()
    elif mode == "rot90":
        return img.rotation_corr(z_rotation=90)
    elif mode == "rot180":
        return img.rotation_corr(z_rotation=180)
    elif mode == "rot270":
        return img.rotation_corr(z_rotation=270)
    elif mode == "selfie":
        return img.mirror()
    elif mode == "upside":
        return img.rotation_corr(z_rotation=180)
    else:
        return img  # normal模式，无变换

# ==================== 组合变换函数 ====================

def mirror_and_flip(img):
    """水平翻转 + 垂直翻转"""
    return img.mirror().flip()

def mirror_and_rotate(img, angle=90):
    """水平翻转 + 旋转"""
    return img.mirror().rotation_corr(z_rotation=angle)

def custom_transform(img, do_mirror=False, do_flip=False, rotation=0):
    """
    自定义组合变换
    Args:
        img: 输入图像
        do_mirror: 是否水平翻转
        do_flip: 是否垂直翻转
        rotation: 旋转角度 (0, 90, 180, 270)
    """
    result = img
    
    if do_mirror:
        result = result.mirror()
    
    if do_flip:
        result = result.flip()
    
    if rotation != 0:
        result = result.rotation_corr(z_rotation=rotation)
    
    return result

# ==================== 使用示例（注释形式） ====================
"""
使用示例:

# 在主程序中导入
from flip_utils import *

# 在图像处理循环中使用
while True:
    img = sensor.snapshot()
    
    # 方法1: 直接调用单个函数
    img = mirror(img)              # 水平翻转
    # img = flip(img)              # 垂直翻转
    # img = rot90(img)             # 顺时针90度
    # img = selfie_mode(img)       # 自拍模式
    
    # 方法2: 使用一键变换
    # img = quick_transform(img, "mirror")
    
    # 方法3: 使用自定义组合
    # img = custom_transform(img, do_mirror=True, rotation=90)
    
    Display.show_image(img)
"""
