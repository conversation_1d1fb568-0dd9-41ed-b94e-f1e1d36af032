import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *

sensor = None

def test_sensor_mirror_functions():
    """测试庐山派K230的各种镜像函数"""
    
    print("=== 庐山派K230镜像函数测试 ===")
    
    # 测试可能的镜像函数名称
    mirror_functions = [
        # 标准命名
        ("set_hmirror", True),
        ("set_vflip", True),
        ("set_hflip", True),
        ("set_vmirror", True),
        
        # 可能的变体命名
        ("set_horizontal_mirror", True),
        ("set_vertical_flip", True),
        ("set_mirror", True),
        ("set_flip", True),
        
        # 庐山派可能的特殊命名
        ("hmirror", True),
        ("vflip", True),
        ("mirror_h", True),
        ("flip_v", True),
        
        # 其他可能的命名
        ("set_mirror_horizontal", True),
        ("set_flip_vertical", True),
        ("horizontal_mirror", True),
        ("vertical_flip", True),
    ]
    
    available_functions = []
    
    for func_name, param in mirror_functions:
        try:
            if hasattr(sensor, func_name):
                print(f"✓ 找到函数: {func_name}")
                available_functions.append(func_name)
                
                # 尝试调用函数
                try:
                    func = getattr(sensor, func_name)
                    func(param)
                    print(f"  ✓ 成功调用: {func_name}({param})")
                except Exception as e:
                    print(f"  ✗ 调用失败: {func_name}({param}) - {e}")
                    
                    # 尝试不同的参数
                    try:
                        func(1)  # 尝试数字参数
                        print(f"  ✓ 成功调用: {func_name}(1)")
                    except:
                        try:
                            func()  # 尝试无参数
                            print(f"  ✓ 成功调用: {func_name}()")
                        except:
                            print(f"  ✗ 所有参数格式都失败")
            else:
                print(f"✗ 未找到函数: {func_name}")
                
        except Exception as e:
            print(f"✗ 测试 {func_name} 时出错: {e}")
    
    return available_functions

def test_sensor_attributes():
    """测试sensor对象的所有属性和方法"""
    print("\n=== Sensor对象属性和方法列表 ===")
    
    sensor_attrs = dir(sensor)
    mirror_related = []
    
    for attr in sensor_attrs:
        if any(keyword in attr.lower() for keyword in ['mirror', 'flip', 'rotate', 'orient']):
            mirror_related.append(attr)
            print(f"发现相关属性/方法: {attr}")
    
    return mirror_related

def try_alternative_approaches():
    """尝试其他可能的镜像方法"""
    print("\n=== 尝试替代方案 ===")
    
    # 方案1: 检查是否有配置字典或参数
    try:
        # 有些版本可能通过配置参数设置
        sensor.set_windowing((0, 0, 640, 640))  # 尝试设置窗口
        print("✓ 支持窗口设置")
    except Exception as e:
        print(f"✗ 窗口设置失败: {e}")
    
    # 方案2: 检查是否有寄存器设置方法
    try:
        # 有些版本可能通过寄存器直接设置
        if hasattr(sensor, 'set_reg'):
            print("✓ 支持寄存器设置")
        else:
            print("✗ 不支持寄存器设置")
    except Exception as e:
        print(f"✗ 寄存器设置检查失败: {e}")
    
    # 方案3: 检查初始化参数
    try:
        # 重新初始化时设置参数
        print("✓ 可以尝试在初始化时设置镜像参数")
    except Exception as e:
        print(f"✗ 初始化参数设置失败: {e}")

try:
    print("庐山派K230镜像函数诊断工具")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    # 基本设置
    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    # 测试镜像函数
    available_functions = test_sensor_mirror_functions()
    
    # 测试sensor属性
    mirror_related_attrs = test_sensor_attributes()
    
    # 尝试替代方案
    try_alternative_approaches()
    
    print(f"\n=== 总结 ===")
    print(f"可用的镜像函数: {available_functions}")
    print(f"相关属性/方法: {mirror_related_attrs}")
    
    # 如果找到可用函数，进行实际测试
    if available_functions:
        print(f"\n=== 实际镜像效果测试 ===")
        
        Display.init(Display.LT9611, to_ide=True)
        MediaManager.init()
        sensor.run()
        clock = time.clock()
        
        test_count = 0
        max_tests = 100  # 测试100帧
        
        while test_count < max_tests:
            clock.tick()
            os.exitpoint()
            
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            
            # 添加标识以观察镜像效果
            img.draw_string_advanced(50, 50, 30, "TEST", color=(255, 0, 0))
            img.draw_circle(100, 100, 20, color=(0, 255, 0), thickness=3, fill=False)
            img.draw_line(50, 200, 200, 200, color=(0, 0, 255), thickness=5)
            
            # 显示测试信息
            img.draw_string_advanced(10, 10, 20, f"Frame: {test_count}", color=(255, 255, 0))
            img.draw_string_advanced(10, 40, 20, f"Available: {len(available_functions)}", color=(255, 255, 0))
            
            if available_functions:
                img.draw_string_advanced(10, 70, 16, f"Functions: {', '.join(available_functions[:3])}", color=(0, 255, 255))
            
            img.compressed_for_ide()
            Display.show_image(img)
            
            test_count += 1
            time.sleep_ms(50)  # 50ms延迟
    
    else:
        print("未找到可用的镜像函数，建议使用图像后处理方法")
        print("请使用我们之前提供的 flip_utils.py 中的函数进行图像翻转")

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
    import traceback
    traceback.print_exc()
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    try:
        Display.deinit()
        MediaManager.deinit()
    except:
        pass
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
