# openmv_simple_shapes.py - OpenMV简化形状检测函数
"""
OpenMV简化形状检测函数库
提供最常用的形状检测和绘制功能，简单易用
"""

import sensor
import image
import time

# ==================== 基础形状检测函数 ====================

def find_and_draw_rectangles(img, threshold=10000, color=(0, 255, 0)):
    """
    检测矩形并画框
    Args:
        img: 输入图像
        threshold: 检测阈值
        color: 绘制颜色 (R, G, B)
    Returns:
        检测到的矩形列表
    """
    # 转换为灰度并二值化
    img_gray = img.to_grayscale(copy=True)
    img_binary = img_gray.binary([(80, 255)])
    
    # 检测矩形
    rectangles = img_binary.find_rects(threshold=threshold)
    
    if rectangles:
        for rect in rectangles:
            # 绘制矩形框
            img.draw_rectangle(rect.rect(), color=color, thickness=2, fill=False)
            
            # 绘制中心点
            center_x = rect.x() + rect.w() // 2
            center_y = rect.y() + rect.h() // 2
            img.draw_circle(center_x, center_y, 3, color=color, thickness=2, fill=True)
            
            # 添加尺寸标签
            img.draw_string(rect.x(), rect.y() - 15, 
                          f"{rect.w()}x{rect.h()}", color=color, scale=1)
    
    return rectangles

def find_and_draw_circles(img, threshold=2000, color=(255, 0, 0)):
    """
    检测圆形并画框
    Args:
        img: 输入图像
        threshold: 检测阈值
        color: 绘制颜色 (R, G, B)
    Returns:
        检测到的圆形列表
    """
    # 转换为灰度并二值化
    img_gray = img.to_grayscale(copy=True)
    img_binary = img_gray.binary([(60, 255)])
    
    # 检测圆形
    circles = img_binary.find_circles(
        threshold=threshold,
        x_margin=10, y_margin=10, r_margin=10,
        r_min=5, r_max=100, r_step=2
    )
    
    if circles:
        for circle in circles:
            x, y, r = circle.x(), circle.y(), circle.r()
            
            # 绘制圆形轮廓
            img.draw_circle(x, y, r, color=color, thickness=2, fill=False)
            
            # 绘制圆心
            img.draw_circle(x, y, 2, color=color, thickness=2, fill=True)
            
            # 添加半径标签
            img.draw_string(x - 15, y - r - 20, f"r={r}", color=color, scale=1)
    
    return circles

def find_and_draw_lines(img, threshold=1000, color=(0, 0, 255)):
    """
    检测直线并绘制
    Args:
        img: 输入图像
        threshold: 检测阈值
        color: 绘制颜色 (R, G, B)
    Returns:
        检测到的直线列表
    """
    # 转换为灰度并二值化
    img_gray = img.to_grayscale(copy=True)
    img_binary = img_gray.binary([(80, 255)])
    
    # 检测直线
    lines = img_binary.find_lines(
        threshold=threshold,
        theta_margin=25,
        rho_margin=25
    )
    
    if lines:
        for line in lines:
            # 绘制直线
            img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), 
                        color=color, thickness=2)
            
            # 在中点添加角度标签
            mid_x = (line.x1() + line.x2()) // 2
            mid_y = (line.y1() + line.y2()) // 2
            img.draw_string(mid_x - 15, mid_y - 10, 
                          f"{line.theta()}°", color=color, scale=1)
    
    return lines

def find_and_draw_blobs(img, thresholds=[(30, 100, 15, 127, 15, 127)], color=(255, 255, 0)):
    """
    检测色块并画框
    Args:
        img: 输入图像
        thresholds: 颜色阈值列表
        color: 绘制颜色 (R, G, B)
    Returns:
        检测到的色块列表
    """
    # 检测色块
    blobs = img.find_blobs(thresholds, pixels_threshold=200, area_threshold=200, merge=True)
    
    if blobs:
        for blob in blobs:
            # 绘制边界框
            img.draw_rectangle(blob.rect(), color=color, thickness=2, fill=False)
            
            # 绘制中心点
            img.draw_circle(blob.cx(), blob.cy(), 3, color=color, thickness=2, fill=True)
            
            # 添加尺寸标签
            img.draw_string(blob.x(), blob.y() - 15, 
                          f"{blob.w()}x{blob.h()}", color=color, scale=1)
    
    return blobs

# ==================== 组合检测函数 ====================

def detect_shapes(img, shapes=['rectangle', 'circle']):
    """
    检测多种形状
    Args:
        img: 输入图像
        shapes: 要检测的形状列表，可选: 'rectangle', 'circle', 'line', 'blob'
    Returns:
        检测结果字典
    """
    results = {}
    
    if 'rectangle' in shapes:
        results['rectangles'] = find_and_draw_rectangles(img)
    
    if 'circle' in shapes:
        results['circles'] = find_and_draw_circles(img)
    
    if 'line' in shapes:
        results['lines'] = find_and_draw_lines(img)
    
    if 'blob' in shapes:
        results['blobs'] = find_and_draw_blobs(img)
    
    return results

def draw_detection_stats(img, results):
    """
    在图像上绘制检测统计信息
    Args:
        img: 图像对象
        results: 检测结果字典
    """
    y_offset = 10
    
    for shape_type, shapes in results.items():
        if shapes:
            count = len(shapes)
            text = f"{shape_type}: {count}"
            img.draw_string(10, y_offset, text, color=(255, 255, 255), scale=2)
            y_offset += 25

# ==================== 一键检测函数 ====================

def auto_detect_all_shapes(img):
    """
    自动检测所有形状并绘制统计信息
    Args:
        img: 输入图像
    Returns:
        检测结果字典
    """
    # 检测所有形状
    results = detect_shapes(img, shapes=['rectangle', 'circle', 'line'])
    
    # 绘制统计信息
    draw_detection_stats(img, results)
    
    return results

# ==================== 使用示例 ====================
"""
使用示例:

import sensor
import image
import time
from openmv_simple_shapes import *

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time=2000)

clock = time.clock()

while True:
    clock.tick()
    img = sensor.snapshot()
    
    # 方法1: 检测单一形状
    rectangles = find_and_draw_rectangles(img)
    # circles = find_and_draw_circles(img)
    # lines = find_and_draw_lines(img)
    
    # 方法2: 检测多种形状
    # results = detect_shapes(img, shapes=['rectangle', 'circle'])
    
    # 方法3: 自动检测所有形状
    # results = auto_detect_all_shapes(img)
    
    # 显示FPS
    img.draw_string(10, img.height() - 20, f"FPS: {clock.fps():.1f}", 
                   color=(255, 255, 0), scale=1)
"""
