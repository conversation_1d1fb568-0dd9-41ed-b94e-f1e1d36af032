import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

# 导入我们的图像变换函数库
from image_transform import *

sensor = None

# 配置变换设置
TRANSFORM_TYPE = "mirror"  # 可选: "normal", "mirror", "flip", "rotate_90", "rotate_180", "rotate_270", "front_camera", etc.

# 或者使用条件变换
USE_CONDITIONAL_TRANSFORM = True
ENABLE_MIRROR = True      # 是否启用水平翻转
ENABLE_FLIP = False       # 是否启用垂直翻转  
ROTATION_ANGLE = 0        # 旋转角度: 0, 90, 180, 270

try:
    print("K230 图像变换主程序")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        
        # 获取原始图像
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 方法1: 使用预定义的变换类型
        if not USE_CONDITIONAL_TRANSFORM:
            img = apply_transform(img, TRANSFORM_TYPE)
        
        # 方法2: 使用条件变换
        else:
            img = conditional_transform(img, 
                                      enable_mirror=ENABLE_MIRROR,
                                      enable_flip=ENABLE_FLIP, 
                                      rotation_angle=ROTATION_ANGLE)

        # 方法3: 直接调用单个函数（注释掉的示例）
        # img = mirror(img)                    # 水平翻转
        # img = flip(img)                      # 垂直翻转
        # img = rotate_90(img)                 # 顺时针90度
        # img = rotate_180(img)                # 180度旋转
        # img = front_camera_selfie(img)       # 前置摄像头效果
        # img = upside_down_camera(img)        # 倒装摄像头校正

        # 方法4: 智能变换（根据摄像头类型自动选择）
        # img = smart_transform(img, camera_type="front")

        # 在图像上添加一些标识，方便观察变换效果
        # 绘制箭头指向右上角
        img.draw_line(50, 100, 150, 50, color=(255, 0, 0), thickness=5)
        img.draw_line(150, 50, 130, 70, color=(255, 0, 0), thickness=3)
        img.draw_line(150, 50, 130, 30, color=(255, 0, 0), thickness=3)
        
        # 绘制四个角落的标识圆圈
        img.draw_circle(50, 50, 15, color=(255, 0, 0), thickness=3, fill=False)      # 左上 - 红色
        img.draw_circle(590, 50, 15, color=(0, 255, 0), thickness=3, fill=False)     # 右上 - 绿色
        img.draw_circle(50, 590, 15, color=(0, 0, 255), thickness=3, fill=False)     # 左下 - 蓝色
        img.draw_circle(590, 590, 15, color=(255, 255, 0), thickness=3, fill=False)  # 右下 - 黄色

        # 显示当前变换信息
        if USE_CONDITIONAL_TRANSFORM:
            transform_info = f"Mirror: {ENABLE_MIRROR}, Flip: {ENABLE_FLIP}, Rotation: {ROTATION_ANGLE}°"
        else:
            transform_info = f"Transform: {TRANSFORM_TYPE}"
        
        img.draw_string_advanced(10, 10, 20, transform_info, color=(255, 255, 255))
        img.draw_string_advanced(10, 40, 20, f"FPS: {clock.fps():.1f}", color=(0, 255, 255))

        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
