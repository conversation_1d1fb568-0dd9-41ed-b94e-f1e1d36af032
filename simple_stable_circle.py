import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

sensor = None

# 简单的圆形稳定性跟踪
last_circles = []
stable_circles = []
frame_count = 0

def is_similar_circle(c1, c2, pos_threshold=25, radius_threshold=15):
    """判断两个圆是否相似"""
    if c1 is None or c2 is None:
        return False
    pos_diff = ((c1.x() - c2.x())**2 + (c1.y() - c2.y())**2)**0.5
    radius_diff = abs(c1.r() - c2.r())
    return pos_diff < pos_threshold and radius_diff < radius_threshold

def update_stable_circles(new_circles):
    """更新稳定的圆形列表"""
    global last_circles, stable_circles
    
    if new_circles is None:
        new_circles = []
    
    # 如果是第一帧，直接使用检测到的圆形
    if not last_circles:
        last_circles = new_circles
        stable_circles = new_circles.copy()
        return stable_circles
    
    # 匹配新检测到的圆形与上一帧的圆形
    matched_circles = []
    for new_circle in new_circles:
        for last_circle in last_circles:
            if is_similar_circle(new_circle, last_circle):
                matched_circles.append(new_circle)
                break
    
    # 更新稳定圆形列表
    stable_circles = matched_circles
    last_circles = new_circles
    
    return stable_circles

try:
    print("simple_stable_circle_detection")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)
        frame_count += 1

        # 图像预处理 - 多种方法组合提高稳定性
        img_gray = img.to_grayscale(copy=True)
        
        # 降噪处理
        img_processed = img_gray.gaussian(1)
        
        # 自适应二值化 - 根据帧数动态调整阈值
        if frame_count % 60 < 20:  # 前1/3时间使用较宽阈值
            binary_threshold = (50, 255)
        elif frame_count % 60 < 40:  # 中间1/3时间使用标准阈值
            binary_threshold = (70, 240)
        else:  # 后1/3时间使用较严格阈值
            binary_threshold = (90, 220)
            
        img_processed = img_processed.binary([binary_threshold])
        
        # 形态学操作
        img_processed = img_processed.erode(1, threshold=1)
        img_processed = img_processed.dilate(2, threshold=1)

        # 圆形检测 - 使用较宽松的参数
        try:
            circles = img_processed.find_circles(
                threshold=1500,        # 较低的阈值
                x_margin=20,          # 较大的位置容差
                y_margin=20,
                r_margin=20,          # 较大的半径容差
                r_min=8,              # 最小半径
                r_max=120,            # 最大半径
                r_step=2              # 半径步长
            )
        except:
            circles = None

        # 更新稳定的圆形
        stable_circles = update_stable_circles(circles)

        # 绘制结果
        if stable_circles:
            for i, circle in enumerate(stable_circles):
                x, y, r = circle.x(), circle.y(), circle.r()
                
                # 画出圆的轮廓 - 稳定的圆用绿色
                img.draw_circle(x, y, r, color=(0, 255, 0), thickness=4, fill=False)
                
                # 圆心标记
                img.draw_circle(x, y, 4, color=(255, 0, 0), thickness=3, fill=True)
                
                # 显示圆的信息
                info_text = "Circle {}: ({},{}) r={}".format(i+1, x, y, r)
                text_y = y - r - 40 if y - r - 40 > 0 else y + r + 20
                img.draw_string_advanced(x-80, text_y, 20, info_text, color=(255, 255, 0))
                
                # 画一个外圈表示检测区域
                img.draw_circle(x, y, r + 10, color=(0, 100, 0), thickness=1, fill=False)

        # 显示所有检测到的圆形（包括不稳定的）用灰色
        if circles:
            for circle in circles:
                if circle not in stable_circles:
                    x, y, r = circle.x(), circle.y(), circle.r()
                    img.draw_circle(x, y, r, color=(128, 128, 128), thickness=2, fill=False)

        # 状态信息
        status_info = [
            "FPS: {:.1f}".format(clock.fps()),
            "Frame: {}".format(frame_count),
            "Detected: {}".format(len(circles) if circles else 0),
            "Stable: {}".format(len(stable_circles)),
            "Threshold: {}-{}".format(binary_threshold[0], binary_threshold[1])
        ]
        
        for i, info in enumerate(status_info):
            img.draw_string_advanced(10, 10 + i * 25, 18, info, color=(255, 0, 0))

        # 在右上角显示当前二值化图像的缩略图（调试用）
        if frame_count % 10 == 0:  # 每10帧更新一次，减少计算量
            try:
                # 缩放二值化图像并显示在右上角
                small_binary = img_processed.copy()
                # 这里可以添加缩放和叠加显示的代码
            except:
                pass

        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
