# image_transform.py - K230图像翻转函数库
"""
K230图像翻转和变换函数库
提供简单易用的图像翻转接口，可直接在主程序中调用
"""

class ImageTransform:
    """图像变换类，提供各种翻转和旋转功能"""
    
    @staticmethod
    def mirror_horizontal(img):
        """
        水平翻转（左右镜像）
        Args:
            img: 输入图像对象
        Returns:
            翻转后的图像对象
        """
        return img.mirror()
    
    @staticmethod
    def flip_vertical(img):
        """
        垂直翻转（上下镜像）
        Args:
            img: 输入图像对象
        Returns:
            翻转后的图像对象
        """
        return img.flip()
    
    @staticmethod
    def rotate_90_cw(img):
        """
        顺时针旋转90度
        Args:
            img: 输入图像对象
        Returns:
            旋转后的图像对象
        """
        return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=90)
    
    @staticmethod
    def rotate_90_ccw(img):
        """
        逆时针旋转90度
        Args:
            img: 输入图像对象
        Returns:
            旋转后的图像对象
        """
        return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=270)
    
    @staticmethod
    def rotate_180(img):
        """
        旋转180度
        Args:
            img: 输入图像对象
        Returns:
            旋转后的图像对象
        """
        return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=180)
    
    @staticmethod
    def rotate_custom(img, angle):
        """
        自定义角度旋转
        Args:
            img: 输入图像对象
            angle: 旋转角度（度）
        Returns:
            旋转后的图像对象
        """
        return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=angle)


# 简化的全局函数接口
def mirror(img):
    """水平翻转（左右镜像）"""
    return img.mirror()

def flip(img):
    """垂直翻转（上下镜像）"""
    return img.flip()

def rotate_90(img):
    """顺时针旋转90度"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=90)

def rotate_180(img):
    """旋转180度"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=180)

def rotate_270(img):
    """逆时针旋转90度（等同于顺时针270度）"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=270)

def rotate(img, angle):
    """自定义角度旋转"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=angle)


# 预设的常用变换组合
def front_camera_selfie(img):
    """前置摄像头自拍效果（水平翻转）"""
    return img.mirror()

def upside_down_camera(img):
    """倒装摄像头校正（180度旋转）"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=180)

def left_side_camera(img):
    """左侧安装摄像头校正（逆时针90度）"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=270)

def right_side_camera(img):
    """右侧安装摄像头校正（顺时针90度）"""
    return img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=90)

def mirror_and_flip(img):
    """水平翻转+垂直翻转"""
    return img.mirror().flip()

def mirror_and_rotate_90(img):
    """水平翻转+顺时针90度旋转"""
    return img.mirror().rotation_corr(x_rotation=0, y_rotation=0, z_rotation=90)


# 动态变换函数
def apply_transform(img, transform_type="normal"):
    """
    根据变换类型应用相应的变换
    Args:
        img: 输入图像
        transform_type: 变换类型字符串
    Returns:
        变换后的图像
    """
    transform_map = {
        "normal": lambda x: x,
        "mirror": lambda x: x.mirror(),
        "flip": lambda x: x.flip(),
        "rotate_90": lambda x: x.rotation_corr(z_rotation=90),
        "rotate_180": lambda x: x.rotation_corr(z_rotation=180),
        "rotate_270": lambda x: x.rotation_corr(z_rotation=270),
        "front_camera": lambda x: x.mirror(),
        "upside_down": lambda x: x.rotation_corr(z_rotation=180),
        "left_side": lambda x: x.rotation_corr(z_rotation=270),
        "right_side": lambda x: x.rotation_corr(z_rotation=90),
        "mirror_flip": lambda x: x.mirror().flip(),
        "mirror_rotate": lambda x: x.mirror().rotation_corr(z_rotation=90)
    }
    
    transform_func = transform_map.get(transform_type, lambda x: x)
    return transform_func(img)


# 批量变换函数
def apply_multiple_transforms(img, transforms):
    """
    应用多个变换
    Args:
        img: 输入图像
        transforms: 变换列表，例如 ["mirror", "rotate_90"]
    Returns:
        变换后的图像
    """
    result = img
    for transform in transforms:
        result = apply_transform(result, transform)
    return result


# 条件变换函数
def conditional_transform(img, enable_mirror=False, enable_flip=False, rotation_angle=0):
    """
    根据条件应用变换
    Args:
        img: 输入图像
        enable_mirror: 是否启用水平翻转
        enable_flip: 是否启用垂直翻转
        rotation_angle: 旋转角度（0, 90, 180, 270）
    Returns:
        变换后的图像
    """
    result = img
    
    if enable_mirror:
        result = result.mirror()
    
    if enable_flip:
        result = result.flip()
    
    if rotation_angle != 0:
        result = result.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=rotation_angle)
    
    return result


# 智能变换函数（根据摄像头类型自动选择）
def smart_transform(img, camera_type="normal", user_preference=None):
    """
    智能变换函数，根据摄像头类型和用户偏好自动选择变换
    Args:
        img: 输入图像
        camera_type: 摄像头类型 ("normal", "front", "upside_down", "left_side", "right_side")
        user_preference: 用户偏好设置字典
    Returns:
        变换后的图像
    """
    # 根据摄像头类型应用基础变换
    if camera_type == "front":
        img = img.mirror()
    elif camera_type == "upside_down":
        img = img.rotation_corr(z_rotation=180)
    elif camera_type == "left_side":
        img = img.rotation_corr(z_rotation=270)
    elif camera_type == "right_side":
        img = img.rotation_corr(z_rotation=90)
    
    # 应用用户偏好
    if user_preference:
        if user_preference.get("additional_mirror", False):
            img = img.mirror()
        if user_preference.get("additional_rotation", 0) != 0:
            img = img.rotation_corr(z_rotation=user_preference["additional_rotation"])
    
    return img


# 获取所有可用的变换类型
def get_available_transforms():
    """返回所有可用的变换类型列表"""
    return [
        "normal", "mirror", "flip", "rotate_90", "rotate_180", "rotate_270",
        "front_camera", "upside_down", "left_side", "right_side",
        "mirror_flip", "mirror_rotate"
    ]


# 变换信息函数
def get_transform_info(transform_type):
    """
    获取变换类型的详细信息
    Args:
        transform_type: 变换类型
    Returns:
        变换信息字典
    """
    info_map = {
        "normal": {"name": "无变换", "description": "保持原始图像"},
        "mirror": {"name": "水平翻转", "description": "左右镜像翻转"},
        "flip": {"name": "垂直翻转", "description": "上下翻转"},
        "rotate_90": {"name": "顺时针90°", "description": "顺时针旋转90度"},
        "rotate_180": {"name": "180°旋转", "description": "旋转180度"},
        "rotate_270": {"name": "逆时针90°", "description": "逆时针旋转90度"},
        "front_camera": {"name": "前置摄像头", "description": "适用于前置摄像头的镜像效果"},
        "upside_down": {"name": "倒装校正", "description": "校正倒装摄像头"},
        "left_side": {"name": "左侧安装", "description": "校正左侧安装的摄像头"},
        "right_side": {"name": "右侧安装", "description": "校正右侧安装的摄像头"},
        "mirror_flip": {"name": "镜像+翻转", "description": "水平翻转加垂直翻转"},
        "mirror_rotate": {"name": "镜像+旋转", "description": "水平翻转加90度旋转"}
    }
    
    return info_map.get(transform_type, {"name": "未知", "description": "未知变换类型"})
