# 圆形识别稳定性问题解决方案

## 问题分析："识别到圆形一下子就没了"

### 常见原因：
1. **参数过于严格** - threshold值太高，容差太小
2. **图像预处理不当** - 二值化阈值不合适
3. **环境光照变化** - 光线变化导致图像质量波动
4. **噪声干扰** - 图像中的噪点影响检测
5. **圆形不够标准** - 实际物体不是完美圆形

## 解决方案

### 1. 参数优化策略

#### 降低检测阈值
```python
# 原来的严格参数
circles = img.find_circles(threshold=10000, r_min=2, r_max=100)

# 优化后的宽松参数
circles = img.find_circles(
    threshold=1500,      # 大幅降低阈值
    x_margin=20,         # 增大位置容差
    y_margin=20,
    r_margin=20,         # 增大半径容差
    r_min=5,            # 适当调整半径范围
    r_max=150,
    r_step=2            # 减小步长提高精度
)
```

#### 动态参数调整
```python
# 根据检测结果动态调整参数
if len(circles) == 0:
    threshold -= 500  # 降低阈值
    r_margin += 5     # 增大容差
elif len(circles) > 10:
    threshold += 500  # 提高阈值
    r_margin -= 2     # 减小容差
```

### 2. 图像预处理改进

#### 多级预处理
```python
# 方法1：标准预处理
img1 = img.gaussian(1)                    # 轻微模糊
img1 = img1.binary([(60, 255)])          # 宽松二值化
img1 = img1.erode(1).dilate(2)           # 形态学操作

# 方法2：增强对比度
img2 = img.histeq()                       # 直方图均衡化
img2 = img2.binary([(80, 220)])          # 标准二值化

# 方法3：边缘增强
img3 = img.gaussian(2)                    # 更强模糊
img3 = img3.binary([(40, 255)])          # 更宽松二值化
```

#### 自适应二值化
```python
# 根据图像亮度自动调整阈值
def adaptive_threshold(img):
    # 计算图像平均亮度
    mean_brightness = img.get_statistics().mean()
    
    if mean_brightness < 100:      # 暗环境
        return (30, 255)
    elif mean_brightness < 150:    # 正常环境
        return (60, 240)
    else:                          # 亮环境
        return (90, 220)
```

### 3. 稳定性跟踪机制

#### 简单跟踪器
```python
class SimpleTracker:
    def __init__(self):
        self.last_circles = []
        self.stable_count = {}
    
    def update(self, new_circles):
        stable = []
        for new_circle in new_circles:
            # 查找相似的历史圆形
            for last_circle in self.last_circles:
                if self.is_similar(new_circle, last_circle):
                    stable.append(new_circle)
                    break
        
        self.last_circles = new_circles
        return stable
```

#### 多帧验证
```python
# 只有连续多帧都检测到的圆形才认为是稳定的
def multi_frame_validation(circles_history, min_frames=3):
    if len(circles_history) < min_frames:
        return []
    
    stable_circles = []
    recent_circles = circles_history[-1]
    
    for circle in recent_circles:
        appear_count = 0
        for hist_circles in circles_history[-min_frames:]:
            if any(is_similar(circle, hist_c) for hist_c in hist_circles):
                appear_count += 1
        
        if appear_count >= min_frames - 1:
            stable_circles.append(circle)
    
    return stable_circles
```

### 4. 环境适应性改进

#### 光照补偿
```python
# 自动曝光调整
def adjust_exposure(sensor, img):
    brightness = img.get_statistics().mean()
    if brightness < 80:
        sensor.set_auto_exposure(False, exposure_us=sensor.get_exposure_us() + 1000)
    elif brightness > 180:
        sensor.set_auto_exposure(False, exposure_us=sensor.get_exposure_us() - 1000)
```

#### 多尺度检测
```python
# 在不同尺度下进行检测
def multi_scale_detection(img):
    all_circles = []
    
    # 原始尺寸
    circles1 = img.find_circles(threshold=2000, r_min=5, r_max=100)
    if circles1: all_circles.extend(circles1)
    
    # 缩小图像检测大圆
    img_small = img.copy().scale(0.5)
    circles2 = img_small.find_circles(threshold=1000, r_min=2, r_max=50)
    if circles2:
        # 将坐标缩放回原始尺寸
        for c in circles2:
            scaled_circle = Circle(c.x()*2, c.y()*2, c.r()*2)
            all_circles.append(scaled_circle)
    
    return all_circles
```

## 调试技巧

### 1. 可视化调试
```python
# 显示二值化结果
binary_img = img.binary([(60, 255)])
# 将二值化图像叠加到原图右上角
img.draw_image(binary_img.scale(0.3), 450, 50)

# 显示检测参数
debug_text = f"Threshold: {threshold}, Circles: {len(circles)}"
img.draw_string_advanced(10, 10, 20, debug_text, color=(255, 255, 0))
```

### 2. 参数实时调整
```python
# 通过按键或时间动态调整参数
frame_count = 0
while True:
    frame_count += 1
    
    # 每100帧调整一次参数
    if frame_count % 100 == 0:
        threshold = max(1000, threshold - 200)  # 逐渐降低阈值
    
    # 检测并显示结果
    circles = img.find_circles(threshold=threshold, ...)
```

### 3. 性能监控
```python
# 监控检测性能
detection_times = []
start_time = time.ticks_ms()

circles = img.find_circles(...)

end_time = time.ticks_ms()
detection_times.append(end_time - start_time)

# 显示平均检测时间
if len(detection_times) > 10:
    avg_time = sum(detection_times[-10:]) / 10
    img.draw_string_advanced(10, 100, 16, f"Avg Detection: {avg_time}ms", color=(0, 255, 255))
```

## 推荐使用方案

1. **快速解决** - 使用 `simple_stable_circle.py`
2. **高级功能** - 使用 `stable_circle_detection.py`
3. **自定义调优** - 基于上述参数调整原代码

## 常见参数组合

### 室内环境
```python
threshold=1500, x_margin=20, y_margin=20, r_margin=15
binary_threshold=(50, 255)
```

### 室外环境
```python
threshold=2500, x_margin=15, y_margin=15, r_margin=10
binary_threshold=(80, 220)
```

### 低光环境
```python
threshold=1000, x_margin=25, y_margin=25, r_margin=20
binary_threshold=(30, 255)
```
