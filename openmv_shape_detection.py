# openmv_shape_detection.py - OpenMV形状识别函数库
"""
OpenMV形状识别和绘制函数库
支持矩形、圆形、直线、色块等多种形状的识别和绘制
"""

import sensor
import image
import time
import math

class OpenMVShapeDetector:
    """OpenMV形状检测器类"""
    
    def __init__(self):
        self.detection_params = {
            'rectangle': {
                'threshold': 10000,
                'merge': True,
                'margin': 5
            },
            'circle': {
                'threshold': 2000,
                'x_margin': 10,
                'y_margin': 10,
                'r_margin': 10,
                'r_min': 2,
                'r_max': 100,
                'r_step': 2
            },
            'line': {
                'threshold': 1000,
                'theta_margin': 25,
                'rho_margin': 25
            },
            'blob': {
                'thresholds': [(30, 100, 15, 127, 15, 127)],  # 通用阈值
                'pixels_threshold': 200,
                'area_threshold': 200,
                'merge': True
            }
        }
        
        self.colors = {
            'rectangle': (0, 255, 0),    # 绿色
            'circle': (255, 0, 0),       # 红色
            'line': (0, 0, 255),         # 蓝色
            'blob': (255, 255, 0)        # 黄色
        }
    
    def detect_rectangles(self, img, draw=True, **kwargs):
        """
        检测矩形并绘制框线
        Args:
            img: 输入图像
            draw: 是否绘制框线
            **kwargs: 额外参数
        Returns:
            检测到的矩形列表
        """
        # 合并参数
        params = self.detection_params['rectangle'].copy()
        params.update(kwargs)
        
        # 转换为灰度图进行检测
        img_gray = img.to_grayscale(copy=True)
        img_binary = img_gray.binary([(80, 255)])
        
        # 检测矩形
        rectangles = img_binary.find_rects(threshold=params['threshold'])
        
        if rectangles and draw:
            for rect in rectangles:
                # 获取矩形的四个角点
                corners = rect.corners()
                
                # 绘制矩形框线
                for i in range(4):
                    next_i = (i + 1) % 4
                    img.draw_line(corners[i][0], corners[i][1], 
                                corners[next_i][0], corners[next_i][1], 
                                color=self.colors['rectangle'], thickness=2)
                
                # 绘制中心点
                center_x = rect.x() + rect.w() // 2
                center_y = rect.y() + rect.h() // 2
                img.draw_circle(center_x, center_y, 3, 
                              color=self.colors['rectangle'], thickness=2, fill=True)
                
                # 添加标签
                img.draw_string(rect.x(), rect.y() - 15, 
                              f"Rect {rect.w()}x{rect.h()}", 
                              color=self.colors['rectangle'], scale=1)
        
        return rectangles
    
    def detect_circles(self, img, draw=True, **kwargs):
        """
        检测圆形并绘制框线
        Args:
            img: 输入图像
            draw: 是否绘制框线
            **kwargs: 额外参数
        Returns:
            检测到的圆形列表
        """
        # 合并参数
        params = self.detection_params['circle'].copy()
        params.update(kwargs)
        
        # 转换为灰度图进行检测
        img_gray = img.to_grayscale(copy=True)
        img_binary = img_gray.binary([(60, 255)])
        
        # 检测圆形
        circles = img_binary.find_circles(
            threshold=params['threshold'],
            x_margin=params['x_margin'],
            y_margin=params['y_margin'],
            r_margin=params['r_margin'],
            r_min=params['r_min'],
            r_max=params['r_max'],
            r_step=params['r_step']
        )
        
        if circles and draw:
            for circle in circles:
                x, y, r = circle.x(), circle.y(), circle.r()
                
                # 绘制圆形轮廓
                img.draw_circle(x, y, r, color=self.colors['circle'], thickness=2, fill=False)
                
                # 绘制圆心
                img.draw_circle(x, y, 2, color=self.colors['circle'], thickness=2, fill=True)
                
                # 添加标签
                img.draw_string(x - 20, y - r - 20, 
                              f"Circle r={r}", 
                              color=self.colors['circle'], scale=1)
        
        return circles
    
    def detect_lines(self, img, draw=True, **kwargs):
        """
        检测直线并绘制
        Args:
            img: 输入图像
            draw: 是否绘制线条
            **kwargs: 额外参数
        Returns:
            检测到的直线列表
        """
        # 合并参数
        params = self.detection_params['line'].copy()
        params.update(kwargs)
        
        # 转换为灰度图进行检测
        img_gray = img.to_grayscale(copy=True)
        img_binary = img_gray.binary([(80, 255)])
        
        # 检测直线
        lines = img_binary.find_lines(
            threshold=params['threshold'],
            theta_margin=params['theta_margin'],
            rho_margin=params['rho_margin']
        )
        
        if lines and draw:
            for line in lines:
                # 绘制直线
                img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), 
                            color=self.colors['line'], thickness=2)
                
                # 计算中点并添加标签
                mid_x = (line.x1() + line.x2()) // 2
                mid_y = (line.y1() + line.y2()) // 2
                img.draw_string(mid_x - 20, mid_y - 10, 
                              f"Line {line.theta()}°", 
                              color=self.colors['line'], scale=1)
        
        return lines
    
    def detect_blobs(self, img, draw=True, **kwargs):
        """
        检测色块并绘制框线
        Args:
            img: 输入图像
            draw: 是否绘制框线
            **kwargs: 额外参数
        Returns:
            检测到的色块列表
        """
        # 合并参数
        params = self.detection_params['blob'].copy()
        params.update(kwargs)
        
        # 检测色块
        blobs = img.find_blobs(
            params['thresholds'],
            pixels_threshold=params['pixels_threshold'],
            area_threshold=params['area_threshold'],
            merge=params['merge']
        )
        
        if blobs and draw:
            for blob in blobs:
                # 绘制边界框
                img.draw_rectangle(blob.rect(), 
                                 color=self.colors['blob'], thickness=2, fill=False)
                
                # 绘制中心点
                img.draw_circle(blob.cx(), blob.cy(), 3, 
                              color=self.colors['blob'], thickness=2, fill=True)
                
                # 添加标签
                img.draw_string(blob.x(), blob.y() - 15, 
                              f"Blob {blob.w()}x{blob.h()}", 
                              color=self.colors['blob'], scale=1)
        
        return blobs
    
    def detect_all_shapes(self, img, shapes=['rectangle', 'circle'], draw=True):
        """
        检测多种形状
        Args:
            img: 输入图像
            shapes: 要检测的形状列表
            draw: 是否绘制
        Returns:
            检测结果字典
        """
        results = {}
        
        if 'rectangle' in shapes:
            results['rectangles'] = self.detect_rectangles(img, draw)
        
        if 'circle' in shapes:
            results['circles'] = self.detect_circles(img, draw)
        
        if 'line' in shapes:
            results['lines'] = self.detect_lines(img, draw)
        
        if 'blob' in shapes:
            results['blobs'] = self.detect_blobs(img, draw)
        
        return results

# 简化的全局函数接口
def detect_and_draw_rectangles(img, **kwargs):
    """检测并绘制矩形"""
    detector = OpenMVShapeDetector()
    return detector.detect_rectangles(img, **kwargs)

def detect_and_draw_circles(img, **kwargs):
    """检测并绘制圆形"""
    detector = OpenMVShapeDetector()
    return detector.detect_circles(img, **kwargs)

def detect_and_draw_lines(img, **kwargs):
    """检测并绘制直线"""
    detector = OpenMVShapeDetector()
    return detector.detect_lines(img, **kwargs)

def detect_and_draw_blobs(img, **kwargs):
    """检测并绘制色块"""
    detector = OpenMVShapeDetector()
    return detector.detect_blobs(img, **kwargs)

def detect_all_shapes(img, shapes=['rectangle', 'circle'], draw=True):
    """检测所有指定形状"""
    detector = OpenMVShapeDetector()
    return detector.detect_all_shapes(img, shapes, draw)

# 自定义绘制函数
def draw_shape_info(img, shape_results):
    """
    在图像上绘制形状统计信息
    Args:
        img: 图像对象
        shape_results: 形状检测结果字典
    """
    y_offset = 10
    
    for shape_type, shapes in shape_results.items():
        if shapes:
            count = len(shapes)
            info_text = f"{shape_type}: {count}"
            img.draw_string(10, y_offset, info_text, color=(255, 255, 255), scale=2)
            y_offset += 25

def enhanced_shape_detection(img, enable_preprocessing=True):
    """
    增强的形状检测函数
    Args:
        img: 输入图像
        enable_preprocessing: 是否启用预处理
    Returns:
        检测结果
    """
    if enable_preprocessing:
        # 图像预处理
        img_processed = img.copy()
        img_processed = img_processed.gaussian(1)  # 高斯模糊降噪
    else:
        img_processed = img
    
    # 创建检测器
    detector = OpenMVShapeDetector()
    
    # 检测所有形状
    results = detector.detect_all_shapes(img_processed, 
                                       shapes=['rectangle', 'circle', 'line'], 
                                       draw=True)
    
    # 绘制统计信息
    draw_shape_info(img, results)
    
    return results
