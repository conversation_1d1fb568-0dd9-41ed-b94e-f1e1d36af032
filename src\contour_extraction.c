typedef struct {
    uint16_t x, y;
} point_t;

typedef struct {
    point_t* points;
    int count;
} contour_t;

// 简化的轮廓跟踪算法
void extract_contours(uint8_t* edge_image, int width, int height, contour_t* contours) {
    uint8_t* visited = calloc(width * height, sizeof(uint8_t));
    int contour_count = 0;
    
    for(int y = 0; y < height; y++) {
        for(int x = 0; x < width; x++) {
            if(edge_image[y*width + x] == 255 && !visited[y*width + x]) {
                // 找到新轮廓起点，开始跟踪
                trace_contour(edge_image, visited, x, y, width, height, 
                             &contours[contour_count++]);
            }
        }
    }
}

void trace_contour(uint8_t* edge, uint8_t* visited, int start_x, int start_y, 
                   int width, int height, contour_t* contour) {
    // 8邻域方向
    int dx[] = {-1, -1, -1, 0, 0, 1, 1, 1};
    int dy[] = {-1, 0, 1, -1, 1, -1, 0, 1};
    
    contour->points = malloc(1000 * sizeof(point_t));
    contour->count = 0;
    
    int x = start_x, y = start_y;
    
    do {
        contour->points[contour->count].x = x;
        contour->points[contour->count].y = y;
        contour->count++;
        visited[y*width + x] = 1;
        
        // 寻找下一个边缘点
        bool found = false;
        for(int i = 0; i < 8; i++) {
            int nx = x + dx[i];
            int ny = y + dy[i];
            
            if(nx >= 0 && nx < width && ny >= 0 && ny < height &&
               edge[ny*width + nx] == 255 && !visited[ny*width + nx]) {
                x = nx;
                y = ny;
                found = true;
                break;
            }
        }
        
        if(!found) break;
        
    } while(contour->count < 1000);
}