# K230 形状识别指南

## 主要修改内容

### 1. 圆形识别修改
原代码中的问题：
```python
# 错误的写法
rects = img_rect.find_circles(threshold(10000))  # 语法错误
```

正确的圆形识别代码：
```python
# 正确的写法
circles = img_circle.find_circles(threshold=10000, x_margin=10, y_margin=10, r_margin=10,
                                r_min=2, r_max=100, r_step=2)
```

### 2. 不同形状识别方法

#### 矩形识别 (find_rects)
```python
rects = img_processed.find_rects(threshold=10000)
# 参数说明：
# - threshold: 面积阈值，过滤掉小的矩形
```

#### 圆形识别 (find_circles)
```python
circles = img_processed.find_circles(
    threshold=10000,    # 面积阈值
    x_margin=10,        # x方向容差
    y_margin=10,        # y方向容差
    r_margin=10,        # 半径容差
    r_min=2,           # 最小半径
    r_max=100,         # 最大半径
    r_step=2           # 半径步长
)
```

#### 直线识别 (find_lines)
```python
lines = img_processed.find_lines(
    threshold=1000,     # 直线强度阈值
    theta_margin=25,    # 角度容差
    rho_margin=25       # 距离容差
)
```

#### 色块识别 (find_blobs)
```python
blobs = img_processed.find_blobs(
    [(0, 255)],         # 颜色阈值范围
    pixels_threshold=200,  # 像素数阈值
    area_threshold=200     # 面积阈值
)
```

## 如何切换不同形状识别

### 方法1：修改SHAPE_MODE变量
在 `shape_detection_template.py` 中修改：
```python
SHAPE_MODE = "circle"     # 圆形
SHAPE_MODE = "rectangle"  # 矩形
SHAPE_MODE = "line"       # 直线
SHAPE_MODE = "blob"       # 色块
```

### 方法2：创建专门的识别函数
```python
def detect_shape(img, shape_type="circle"):
    img_processed = img.to_grayscale(copy=True)
    img_processed = img_processed.binary([(82, 212)])
    
    if shape_type == "circle":
        return img_processed.find_circles(threshold=10000)
    elif shape_type == "rectangle":
        return img_processed.find_rects(threshold=10000)
    elif shape_type == "line":
        return img_processed.find_lines(threshold=1000)
    elif shape_type == "blob":
        return img_processed.find_blobs([(0, 255)], pixels_threshold=200)
```

## 参数调优建议

### 1. 二值化阈值调整
```python
img_processed = img_processed.binary([(82, 212)])  # 调整这个范围
```

### 2. 形状检测阈值调整
- **threshold**: 控制检测敏感度，值越大越严格
- **margin参数**: 控制检测容差，值越大越宽松
- **size参数**: 控制检测的尺寸范围

### 3. 环境适应性调整
- 光照条件差：降低二值化下限
- 噪声较多：提高threshold值
- 形状不规则：增大margin参数

## 绘制轮廓的不同方式

### 圆形轮廓
```python
# 只画轮廓
img.draw_circle(x, y, r, color=(0, 255, 0), thickness=3, fill=False)
# 填充圆形
img.draw_circle(x, y, r, color=(0, 255, 0), thickness=3, fill=True)
```

### 矩形轮廓
```python
# 方法1：使用corners画线
corners = rect.corners()
for i in range(4):
    next_i = (i + 1) % 4
    img.draw_line(corners[i][0], corners[i][1], corners[next_i][0], corners[next_i][1], 
                  color=(0, 255, 0), thickness=3)

# 方法2：直接画矩形框
img.draw_rectangle(rect.x(), rect.y(), rect.w(), rect.h(), 
                   color=(0, 255, 0), thickness=3, fill=False)
```

## 性能优化建议

1. **降低图像分辨率**：如果不需要高精度，可以降低sensor分辨率
2. **ROI设置**：只在感兴趣区域进行检测
3. **帧率控制**：根据需要调整处理帧率
4. **参数预设**：为不同场景预设参数组合
