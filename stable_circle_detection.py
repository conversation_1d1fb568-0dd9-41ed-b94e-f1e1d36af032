import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

sensor = None

# 圆形跟踪和稳定性参数
class CircleTracker:
    def __init__(self, max_history=5, position_threshold=30, radius_threshold=10):
        self.history = []
        self.max_history = max_history
        self.position_threshold = position_threshold
        self.radius_threshold = radius_threshold
        self.stable_circles = []
    
    def update(self, new_circles):
        if new_circles is None:
            new_circles = []
        
        # 更新历史记录
        self.history.append(new_circles)
        if len(self.history) > self.max_history:
            self.history.pop(0)
        
        # 计算稳定的圆形
        self.stable_circles = self._get_stable_circles()
        return self.stable_circles
    
    def _get_stable_circles(self):
        if len(self.history) < 2:
            return self.history[-1] if self.history else []
        
        stable = []
        recent_circles = self.history[-1]  # 最新检测到的圆形
        
        for circle in recent_circles:
            # 检查这个圆形在历史中是否稳定出现
            stability_count = 0
            for hist_circles in self.history[-3:]:  # 检查最近3帧
                for hist_circle in hist_circles:
                    if self._circles_similar(circle, hist_circle):
                        stability_count += 1
                        break
            
            # 如果在多帧中都出现，认为是稳定的
            if stability_count >= 2:
                stable.append(circle)
        
        return stable
    
    def _circles_similar(self, c1, c2):
        pos_diff = ((c1.x() - c2.x())**2 + (c1.y() - c2.y())**2)**0.5
        radius_diff = abs(c1.r() - c2.r())
        return pos_diff < self.position_threshold and radius_diff < self.radius_threshold

# 创建圆形跟踪器
circle_tracker = CircleTracker()

try:
    print("stable_circle_detection_test")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    # 多种检测参数组合
    detection_params = [
        # 参数组1：标准检测
        {"threshold": 2000, "x_margin": 15, "y_margin": 15, "r_margin": 15, 
         "r_min": 5, "r_max": 150, "r_step": 2},
        # 参数组2：敏感检测
        {"threshold": 1000, "x_margin": 20, "y_margin": 20, "r_margin": 20, 
         "r_min": 3, "r_max": 200, "r_step": 1},
        # 参数组3：粗糙检测
        {"threshold": 5000, "x_margin": 10, "y_margin": 10, "r_margin": 10, 
         "r_min": 10, "r_max": 100, "r_step": 3}
    ]
    
    current_param_set = 0

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 多种图像预处理方法
        img_circle = img.to_grayscale(copy=True)
        
        # 方法1：标准预处理
        img_processed1 = img_circle.copy()
        img_processed1 = img_processed1.gaussian(1)
        img_processed1 = img_processed1.binary([(60, 255)])
        img_processed1 = img_processed1.erode(1, threshold=1)
        img_processed1 = img_processed1.dilate(1, threshold=1)
        
        # 方法2：强化对比度
        img_processed2 = img_circle.copy()
        img_processed2 = img_processed2.histeq()  # 直方图均衡化
        img_processed2 = img_processed2.binary([(80, 220)])
        
        # 方法3：边缘检测辅助
        img_processed3 = img_circle.copy()
        img_processed3 = img_processed3.gaussian(2)
        img_processed3 = img_processed3.binary([(40, 255)])
        
        # 使用当前参数组进行检测
        params = detection_params[current_param_set]
        
        all_circles = []
        
        # 在多个预处理结果上进行检测
        for img_proc in [img_processed1, img_processed2, img_processed3]:
            try:
                circles = img_proc.find_circles(**params)
                if circles:
                    all_circles.extend(circles)
            except:
                continue
        
        # 去重相似的圆形
        unique_circles = []
        for circle in all_circles:
            is_duplicate = False
            for existing in unique_circles:
                if circle_tracker._circles_similar(circle, existing):
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_circles.append(circle)
        
        # 使用跟踪器获取稳定的圆形
        stable_circles = circle_tracker.update(unique_circles)

        # 绘制检测结果
        if stable_circles:
            for i, circle in enumerate(stable_circles):
                x, y, r = circle.x(), circle.y(), circle.r()
                
                # 画出圆的轮廓 - 使用不同颜色表示稳定性
                color = (0, 255, 0)  # 绿色表示稳定
                img.draw_circle(x, y, r, color=color, thickness=3, fill=False)
                
                # 圆心标记
                img.draw_circle(x, y, 3, color=(255, 0, 0), thickness=2, fill=True)
                
                # 显示圆的信息
                info_text = "Stable Circle {}: r={}".format(i+1, r)
                img.draw_string_advanced(x-60, y-r-35, 18, info_text, color=(255, 255, 0))
        
        # 显示不稳定的圆形（半透明）
        recent_circles = circle_tracker.history[-1] if circle_tracker.history else []
        for circle in recent_circles:
            if circle not in stable_circles:
                x, y, r = circle.x(), circle.y(), circle.r()
                # 用虚线或不同颜色表示不稳定的检测
                img.draw_circle(x, y, r, color=(100, 100, 100), thickness=1, fill=False)

        # 状态信息
        status_text = "fps: {} | Param Set: {} | Stable: {} | Total: {}".format(
            clock.fps(), current_param_set + 1, len(stable_circles), len(recent_circles))
        img.draw_string_advanced(10, 10, 16, status_text, color=(255, 0, 0))
        
        # 动态切换参数组（可选）
        frame_count = clock.fps() * 10  # 每10秒切换一次参数组
        if int(frame_count) % 300 == 0:  # 大约每10秒
            current_param_set = (current_param_set + 1) % len(detection_params)

        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
