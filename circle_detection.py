import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

sensor = None

try:
    print("circle_detection_test")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    # 鼠标悬停在函数上可以查看允许接收的参数
    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 圆形识别，用来找圆形并画出轮廓
        img_circle = img.to_grayscale(copy=True)
        img_circle = img_circle.binary([(82, 212)])
        
        # 使用find_circles函数识别圆形
        circles = img_circle.find_circles(threshold=10000, x_margin=10, y_margin=10, r_margin=10,
                                        r_min=2, r_max=100, r_step=2)

        if circles is not None:
            for circle in circles:
                # 获取圆心坐标和半径
                x = circle.x()
                y = circle.y()
                r = circle.r()
                
                # 画出圆的轮廓
                img.draw_circle(x, y, r, color=(0, 255, 0), thickness=3, fill=False)
                
                # 在圆心画一个小点
                img.draw_circle(x, y, 2, color=(255, 0, 0), thickness=2, fill=True)
                
                # 显示圆的信息
                info_text = "Circle: ({},{}) r={}".format(x, y, r)
                img.draw_string_advanced(x-50, y-r-30, 20, info_text, color=(255, 255, 0))

        img.draw_string_advanced(50, 50, 80, "fps: {}".format(clock.fps()), color=(255, 0, 0))
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
