# openmv_complete_example.py - OpenMV完整形状检测示例
"""
OpenMV完整形状检测示例程序
展示如何使用简化的形状检测函数
"""

import sensor
import image
import time

# 导入简化的形状检测函数
from openmv_simple_shapes import *

# ==================== 摄像头初始化 ====================
print("初始化OpenMV摄像头...")

sensor.reset()                      # 重置摄像头
sensor.set_pixformat(sensor.RGB565) # 设置像素格式为RGB565
sensor.set_framesize(sensor.QVGA)   # 设置帧大小为QVGA (320x240)
sensor.skip_frames(time=2000)       # 跳过前2秒的帧，让摄像头稳定

# 可选：设置摄像头参数
sensor.set_auto_gain(False)         # 关闭自动增益
sensor.set_auto_whitebal(False)     # 关闭自动白平衡

print("摄像头初始化完成")

# ==================== 检测模式设置 ====================
detection_modes = [
    {"name": "矩形检测", "func": "rectangle"},
    {"name": "圆形检测", "func": "circle"},
    {"name": "直线检测", "func": "line"},
    {"name": "色块检测", "func": "blob"},
    {"name": "矩形+圆形", "func": "rect_circle"},
    {"name": "全部形状", "func": "all"}
]

current_mode = 0
last_mode_switch = time.ticks_ms()
mode_switch_interval = 4000  # 4秒切换一次

print("开始形状检测...")
print("程序将自动切换检测模式")

clock = time.clock()

# ==================== 主循环 ====================
while True:
    clock.tick()
    
    # 获取图像
    img = sensor.snapshot()
    
    # 自动切换检测模式
    current_time = time.ticks_ms()
    if current_time - last_mode_switch > mode_switch_interval:
        current_mode = (current_mode + 1) % len(detection_modes)
        last_mode_switch = current_time
        mode_info = detection_modes[current_mode]
        print(f"切换到: {mode_info['name']}")
    
    # 获取当前模式
    mode_info = detection_modes[current_mode]
    mode_func = mode_info['func']
    
    # 根据模式执行相应的检测
    if mode_func == "rectangle":
        # 只检测矩形
        rectangles = find_and_draw_rectangles(img)
        
        # 显示检测结果
        if rectangles:
            img.draw_string(10, 10, f"矩形: {len(rectangles)}", 
                          color=(255, 255, 255), scale=2)
            
            # 显示每个矩形的详细信息
            for i, rect in enumerate(rectangles[:3]):  # 最多显示3个
                info = f"R{i+1}: {rect.w()}x{rect.h()}"
                img.draw_string(10, 35 + i*20, info, color=(0, 255, 0), scale=1)
        else:
            img.draw_string(10, 10, "未检测到矩形", color=(255, 255, 255), scale=2)
    
    elif mode_func == "circle":
        # 只检测圆形
        circles = find_and_draw_circles(img)
        
        # 显示检测结果
        if circles:
            img.draw_string(10, 10, f"圆形: {len(circles)}", 
                          color=(255, 255, 255), scale=2)
            
            # 显示每个圆形的详细信息
            for i, circle in enumerate(circles[:3]):  # 最多显示3个
                info = f"C{i+1}: r={circle.r()}"
                img.draw_string(10, 35 + i*20, info, color=(255, 0, 0), scale=1)
        else:
            img.draw_string(10, 10, "未检测到圆形", color=(255, 255, 255), scale=2)
    
    elif mode_func == "line":
        # 只检测直线
        lines = find_and_draw_lines(img)
        
        # 显示检测结果
        if lines:
            img.draw_string(10, 10, f"直线: {len(lines)}", 
                          color=(255, 255, 255), scale=2)
            
            # 显示每条直线的详细信息
            for i, line in enumerate(lines[:3]):  # 最多显示3条
                info = f"L{i+1}: {line.theta()}°"
                img.draw_string(10, 35 + i*20, info, color=(0, 0, 255), scale=1)
        else:
            img.draw_string(10, 10, "未检测到直线", color=(255, 255, 255), scale=2)
    
    elif mode_func == "blob":
        # 只检测色块
        blobs = find_and_draw_blobs(img)
        
        # 显示检测结果
        if blobs:
            img.draw_string(10, 10, f"色块: {len(blobs)}", 
                          color=(255, 255, 255), scale=2)
            
            # 显示每个色块的详细信息
            for i, blob in enumerate(blobs[:3]):  # 最多显示3个
                info = f"B{i+1}: {blob.w()}x{blob.h()}"
                img.draw_string(10, 35 + i*20, info, color=(255, 255, 0), scale=1)
        else:
            img.draw_string(10, 10, "未检测到色块", color=(255, 255, 255), scale=2)
    
    elif mode_func == "rect_circle":
        # 检测矩形和圆形
        results = detect_shapes(img, shapes=['rectangle', 'circle'])
        
        # 显示统计信息
        y_pos = 10
        total_shapes = 0
        for shape_type, shapes in results.items():
            if shapes:
                count = len(shapes)
                total_shapes += count
                text = f"{shape_type}: {count}"
                img.draw_string(10, y_pos, text, color=(255, 255, 255), scale=1)
                y_pos += 20
        
        if total_shapes == 0:
            img.draw_string(10, 10, "未检测到形状", color=(255, 255, 255), scale=2)
    
    elif mode_func == "all":
        # 检测所有形状
        results = auto_detect_all_shapes(img)
        
        # 统计总数
        total_shapes = sum(len(shapes) for shapes in results.values() if shapes)
        if total_shapes == 0:
            img.draw_string(10, 120, "未检测到任何形状", color=(255, 255, 255), scale=1)
    
    # 显示当前模式和状态信息
    img.draw_string(10, img.height() - 60, f"模式: {mode_info['name']}", 
                   color=(255, 255, 0), scale=1)
    img.draw_string(10, img.height() - 40, f"FPS: {clock.fps():.1f}", 
                   color=(255, 255, 0), scale=1)
    img.draw_string(10, img.height() - 20, f"帧: {clock.frame_count}", 
                   color=(255, 255, 0), scale=1)
    
    # 在右上角显示模式编号
    img.draw_string(img.width() - 50, 10, f"{current_mode + 1}/{len(detection_modes)}", 
                   color=(0, 255, 255), scale=2)
    
    # 每秒打印一次状态到控制台
    if clock.frame_count % 30 == 0:  # 假设30FPS
        print(f"[{mode_info['name']}] FPS: {clock.fps():.1f}, 帧数: {clock.frame_count}")

# 注意：这个程序会一直运行，在OpenMV IDE中按停止按钮来停止程序
