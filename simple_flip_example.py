import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *

# 导入翻转工具函数
from flip_utils import *

sensor = None

try:
    print("简单翻转示例")

    sensor = Sensor(width=640, height=640)
    sensor.reset()
    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        
        # 获取图像
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # ============ 在这里调用翻转函数 ============
        
        # 选择一种翻转方式（取消注释想要的效果）:
        
        img = mirror(img)                    # 水平翻转（左右镜像）
        # img = flip(img)                    # 垂直翻转（上下翻转）
        # img = rot90(img)                   # 顺时针旋转90度
        # img = rot180(img)                  # 旋转180度
        # img = rot270(img)                  # 逆时针旋转90度
        # img = selfie_mode(img)             # 自拍模式（等同于mirror）
        # img = fix_upside_down(img)         # 修正倒装摄像头
        
        # 或者使用一键变换:
        # img = quick_transform(img, "mirror")   # 水平翻转
        # img = quick_transform(img, "rot90")    # 顺时针90度
        
        # 或者使用自定义组合:
        # img = custom_transform(img, do_mirror=True, do_flip=False, rotation=0)
        
        # ==========================================

        # 添加标识方便观察效果
        img.draw_string_advanced(10, 10, 30, "FLIPPED", color=(255, 0, 0))
        img.draw_circle(50, 50, 20, color=(0, 255, 0), thickness=3, fill=False)  # 左上角标识
        
        img.draw_string_advanced(10, 50, 20, f"FPS: {clock.fps():.1f}", color=(255, 255, 0))
        
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
