import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *

sensor = None

class LushanK230Mirror:
    """庐山派K230镜像兼容性类"""
    
    def __init__(self, sensor_obj):
        self.sensor = sensor_obj
        self.mirror_method = None
        self.flip_method = None
        self.available_functions = []
        self._detect_available_functions()
    
    def _detect_available_functions(self):
        """检测可用的镜像函数"""
        
        # 庐山派K230可能的镜像函数名称
        possible_functions = [
            # 标准命名
            'set_hmirror', 'set_vflip', 'set_hflip', 'set_vmirror',
            # 简化命名
            'hmirror', 'vflip', 'hflip', 'vmirror',
            # 完整命名
            'set_horizontal_mirror', 'set_vertical_flip',
            'set_mirror_horizontal', 'set_flip_vertical',
            # 其他可能的命名
            'mirror_h', 'flip_v', 'set_mirror', 'set_flip'
        ]
        
        for func_name in possible_functions:
            if hasattr(self.sensor, func_name):
                self.available_functions.append(func_name)
                print(f"发现可用函数: {func_name}")
                
                # 设置默认的镜像和翻转方法
                if 'mirror' in func_name.lower() or 'hmirror' in func_name.lower():
                    if not self.mirror_method:
                        self.mirror_method = func_name
                
                if 'flip' in func_name.lower() or 'vflip' in func_name.lower():
                    if not self.flip_method:
                        self.flip_method = func_name
    
    def set_horizontal_mirror(self, enable=True):
        """设置水平镜像"""
        if self.mirror_method:
            try:
                func = getattr(self.sensor, self.mirror_method)
                func(enable)
                print(f"✓ 水平镜像设置成功: {self.mirror_method}({enable})")
                return True
            except Exception as e:
                print(f"✗ 水平镜像设置失败: {e}")
                # 尝试不同的参数格式
                try:
                    func(1 if enable else 0)
                    print(f"✓ 水平镜像设置成功 (数字参数): {self.mirror_method}({1 if enable else 0})")
                    return True
                except Exception as e2:
                    print(f"✗ 数字参数也失败: {e2}")
        else:
            print("✗ 未找到水平镜像函数")
        return False
    
    def set_vertical_flip(self, enable=True):
        """设置垂直翻转"""
        if self.flip_method:
            try:
                func = getattr(self.sensor, self.flip_method)
                func(enable)
                print(f"✓ 垂直翻转设置成功: {self.flip_method}({enable})")
                return True
            except Exception as e:
                print(f"✗ 垂直翻转设置失败: {e}")
                # 尝试不同的参数格式
                try:
                    func(1 if enable else 0)
                    print(f"✓ 垂直翻转设置成功 (数字参数): {self.flip_method}({1 if enable else 0})")
                    return True
                except Exception as e2:
                    print(f"✗ 数字参数也失败: {e2}")
        else:
            print("✗ 未找到垂直翻转函数")
        return False
    
    def try_all_combinations(self):
        """尝试所有可能的函数组合"""
        print("\n=== 尝试所有可能的镜像设置 ===")
        
        for func_name in self.available_functions:
            func = getattr(self.sensor, func_name)
            
            # 尝试不同的参数
            for param in [True, False, 1, 0]:
                try:
                    func(param)
                    print(f"✓ {func_name}({param}) - 成功")
                except Exception as e:
                    print(f"✗ {func_name}({param}) - 失败: {e}")
            
            # 尝试无参数调用
            try:
                func()
                print(f"✓ {func_name}() - 成功")
            except Exception as e:
                print(f"✗ {func_name}() - 失败: {e}")
    
    def get_status(self):
        """获取当前状态"""
        return {
            'available_functions': self.available_functions,
            'mirror_method': self.mirror_method,
            'flip_method': self.flip_method,
            'has_mirror': self.mirror_method is not None,
            'has_flip': self.flip_method is not None
        }

def test_lushan_mirror():
    """测试庐山派镜像功能"""
    global sensor
    
    try:
        print("=== 庐山派K230镜像功能测试 ===")
        
        sensor = Sensor(width=640, height=640)
        sensor.reset()
        
        # 创建镜像控制器
        mirror_controller = LushanK230Mirror(sensor)
        
        # 显示检测结果
        status = mirror_controller.get_status()
        print(f"\n检测结果:")
        print(f"可用函数: {status['available_functions']}")
        print(f"镜像方法: {status['mirror_method']}")
        print(f"翻转方法: {status['flip_method']}")
        print(f"支持镜像: {status['has_mirror']}")
        print(f"支持翻转: {status['has_flip']}")
        
        # 基本设置
        sensor.set_framesize(width=640, height=640)
        sensor.set_pixformat(Sensor.RGB565)
        
        # 如果有可用函数，尝试设置
        if status['available_functions']:
            print(f"\n=== 尝试设置镜像 ===")
            
            # 尝试设置水平镜像
            mirror_controller.set_horizontal_mirror(True)
            time.sleep(0.1)
            
            # 尝试设置垂直翻转
            mirror_controller.set_vertical_flip(True)
            time.sleep(0.1)
            
            # 尝试所有组合
            mirror_controller.try_all_combinations()
        
        # 启动显示测试
        Display.init(Display.LT9611, to_ide=True)
        MediaManager.init()
        sensor.run()
        clock = time.clock()
        
        print(f"\n=== 开始图像测试 ===")
        
        frame_count = 0
        test_modes = [
            {"name": "原始", "mirror": False, "flip": False},
            {"name": "镜像", "mirror": True, "flip": False},
            {"name": "翻转", "mirror": False, "flip": True},
            {"name": "镜像+翻转", "mirror": True, "flip": True}
        ]
        
        current_mode = 0
        last_switch = time.ticks_ms()
        
        while frame_count < 200:  # 测试200帧
            clock.tick()
            os.exitpoint()
            
            # 每50帧切换一次模式
            current_time = time.ticks_ms()
            if current_time - last_switch > 2000:  # 2秒切换
                current_mode = (current_mode + 1) % len(test_modes)
                mode = test_modes[current_mode]
                
                print(f"切换到模式: {mode['name']}")
                
                # 尝试设置镜像
                if status['has_mirror']:
                    mirror_controller.set_horizontal_mirror(mode['mirror'])
                
                if status['has_flip']:
                    mirror_controller.set_vertical_flip(mode['flip'])
                
                last_switch = current_time
            
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            
            # 添加测试标识
            img.draw_string_advanced(50, 50, 40, "LUSHAN", color=(255, 0, 0))
            img.draw_circle(100, 100, 30, color=(0, 255, 0), thickness=5, fill=False)
            img.draw_line(50, 200, 250, 200, color=(0, 0, 255), thickness=8)
            
            # 显示当前模式
            mode = test_modes[current_mode]
            img.draw_string_advanced(10, 10, 20, f"Mode: {mode['name']}", color=(255, 255, 0))
            img.draw_string_advanced(10, 40, 20, f"Frame: {frame_count}", color=(255, 255, 0))
            img.draw_string_advanced(10, 70, 16, f"Mirror: {mode['mirror']}, Flip: {mode['flip']}", color=(0, 255, 255))
            
            # 显示函数状态
            if status['available_functions']:
                img.draw_string_advanced(10, 100, 14, f"Functions: {len(status['available_functions'])}", color=(255, 255, 255))
            else:
                img.draw_string_advanced(10, 100, 14, "No sensor functions", color=(255, 0, 0))
            
            img.compressed_for_ide()
            Display.show_image(img)
            
            frame_count += 1
        
        print(f"\n=== 测试完成 ===")
        print("如果sensor函数不可用，请使用图像后处理方法")
        
    except Exception as e:
        print(f"测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        test_lushan_mirror()
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        if sensor:
            try:
                sensor.stop()
            except:
                pass
        try:
            Display.deinit()
            MediaManager.deinit()
        except:
            pass
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
