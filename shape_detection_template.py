import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

sensor = None

# 形状识别模式选择
SHAPE_MODE = "circle"  # 可选: "rectangle", "circle", "line", "blob"

# 图像翻转设置
ENABLE_MIRROR = False    # 是否启用水平翻转（左右镜像）
ENABLE_FLIP = False      # 是否启用垂直翻转（上下镜像）
ROTATION_ANGLE = 0       # 旋转角度：0, 90, 180, 270

try:
    print(f"{SHAPE_MODE}_detection_test")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 图像翻转处理
        if ENABLE_MIRROR:
            img = img.mirror()      # 水平翻转（左右镜像）

        if ENABLE_FLIP:
            img = img.flip()        # 垂直翻转（上下镜像）

        if ROTATION_ANGLE != 0:
            img = img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=ROTATION_ANGLE)

        # 图像预处理
        img_processed = img.to_grayscale(copy=True)
        img_processed = img_processed.binary([(82, 212)])

        if SHAPE_MODE == "rectangle":
            # 矩形识别
            shapes = img_processed.find_rects(threshold=10000)
            if shapes is not None:
                for rect in shapes:
                    # 获取矩形的四个角点
                    corners = rect.corners()
                    # 画出矩形轮廓
                    img.draw_line(corners[0][0], corners[0][1], corners[1][0], corners[1][1], color=(0, 255, 0), thickness=3)
                    img.draw_line(corners[1][0], corners[1][1], corners[2][0], corners[2][1], color=(0, 255, 0), thickness=3)
                    img.draw_line(corners[2][0], corners[2][1], corners[3][0], corners[3][1], color=(0, 255, 0), thickness=3)
                    img.draw_line(corners[3][0], corners[3][1], corners[0][0], corners[0][1], color=(0, 255, 0), thickness=3)
                    
                    # 显示矩形信息
                    center_x = rect.x() + rect.w()//2
                    center_y = rect.y() + rect.h()//2
                    info_text = "Rect: {}x{}".format(rect.w(), rect.h())
                    img.draw_string_advanced(center_x-40, center_y-10, 20, info_text, color=(255, 255, 0))

        elif SHAPE_MODE == "circle":
            # 圆形识别
            shapes = img_processed.find_circles(threshold=10000, x_margin=10, y_margin=10, r_margin=10,
                                              r_min=2, r_max=100, r_step=2)
            if shapes is not None:
                for circle in shapes:
                    x, y, r = circle.x(), circle.y(), circle.r()
                    # 画出圆的轮廓
                    img.draw_circle(x, y, r, color=(0, 255, 0), thickness=3, fill=False)
                    # 圆心标记
                    img.draw_circle(x, y, 2, color=(255, 0, 0), thickness=2, fill=True)
                    # 显示圆信息
                    info_text = "Circle: r={}".format(r)
                    img.draw_string_advanced(x-30, y-r-25, 20, info_text, color=(255, 255, 0))

        elif SHAPE_MODE == "line":
            # 直线识别
            shapes = img_processed.find_lines(threshold=1000, theta_margin=25, rho_margin=25)
            if shapes is not None:
                for line in shapes:
                    # 画出直线
                    img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), color=(0, 255, 0), thickness=3)
                    # 显示直线信息
                    mid_x = (line.x1() + line.x2()) // 2
                    mid_y = (line.y1() + line.y2()) // 2
                    info_text = "Line: {}°".format(line.theta())
                    img.draw_string_advanced(mid_x-30, mid_y-15, 20, info_text, color=(255, 255, 0))

        elif SHAPE_MODE == "blob":
            # 色块识别
            shapes = img_processed.find_blobs([(0, 255)], pixels_threshold=200, area_threshold=200)
            if shapes is not None:
                for blob in shapes:
                    # 画出色块边界框
                    img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(), 
                                     color=(0, 255, 0), thickness=3, fill=False)
                    # 画出色块中心
                    img.draw_circle(blob.cx(), blob.cy(), 3, color=(255, 0, 0), thickness=2, fill=True)
                    # 显示色块信息
                    info_text = "Blob: {}x{}".format(blob.w(), blob.h())
                    img.draw_string_advanced(blob.cx()-30, blob.cy()-20, 20, info_text, color=(255, 255, 0))

        img.draw_string_advanced(50, 50, 80, "fps: {} Mode: {}".format(clock.fps(), SHAPE_MODE), color=(255, 0, 0))
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
