# K230 图像翻转和变换函数完整指南

## 基本翻转函数

### 1. 水平翻转（左右镜像）
```python
img_mirrored = img.mirror()
```
- **功能**: 将图像沿垂直轴翻转，左右互换
- **效果**: 镜像效果，就像照镜子一样
- **应用场景**: 前置摄像头自拍效果、左右手习惯适配

### 2. 垂直翻转（上下镜像）
```python
img_flipped = img.flip()
```
- **功能**: 将图像沿水平轴翻转，上下互换
- **效果**: 图像倒置
- **应用场景**: 倒装摄像头、特殊安装角度

### 3. 旋转变换
```python
# 方法1：使用rotation_corr（推荐）
img_rotated = img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=90)

# 方法2：使用rotate（如果支持）
img_rotated = img.rotate(angle=90)
```
- **参数说明**:
  - `x_rotation`: 绕X轴旋转角度
  - `y_rotation`: 绕Y轴旋转角度  
  - `z_rotation`: 绕Z轴旋转角度（平面旋转）
- **常用角度**: 0°, 90°, 180°, 270°

## 组合变换

### 1. 水平翻转 + 垂直翻转 = 180°旋转
```python
img_transformed = img.mirror().flip()
# 等效于
img_transformed = img.rotation_corr(z_rotation=180)
```

### 2. 镜像 + 旋转组合
```python
# 先镜像再旋转
img_transformed = img.mirror().rotation_corr(z_rotation=90)

# 先旋转再镜像
img_transformed = img.rotation_corr(z_rotation=90).mirror()
```

## 实际应用示例

### 1. 摄像头方向校正
```python
def correct_camera_orientation(img, camera_position="front"):
    """根据摄像头位置校正图像方向"""
    if camera_position == "front":
        # 前置摄像头通常需要水平翻转
        return img.mirror()
    elif camera_position == "back_inverted":
        # 后置摄像头倒装
        return img.rotation_corr(z_rotation=180)
    elif camera_position == "side_left":
        # 侧装摄像头（左侧）
        return img.rotation_corr(z_rotation=90)
    elif camera_position == "side_right":
        # 侧装摄像头（右侧）
        return img.rotation_corr(z_rotation=270)
    else:
        return img  # 正常安装，无需处理
```

### 2. 用户界面适配
```python
def adapt_for_user_preference(img, user_settings):
    """根据用户设置调整图像"""
    if user_settings.get("mirror_mode", False):
        img = img.mirror()
    
    if user_settings.get("rotation", 0) != 0:
        img = img.rotation_corr(z_rotation=user_settings["rotation"])
    
    return img
```

### 3. 动态翻转切换
```python
class ImageTransformer:
    def __init__(self):
        self.transform_modes = [
            {"name": "normal", "func": lambda x: x},
            {"name": "mirror", "func": lambda x: x.mirror()},
            {"name": "flip", "func": lambda x: x.flip()},
            {"name": "rotate_90", "func": lambda x: x.rotation_corr(z_rotation=90)},
            {"name": "rotate_180", "func": lambda x: x.rotation_corr(z_rotation=180)},
            {"name": "rotate_270", "func": lambda x: x.rotation_corr(z_rotation=270)}
        ]
        self.current_mode = 0
    
    def next_mode(self):
        self.current_mode = (self.current_mode + 1) % len(self.transform_modes)
    
    def transform(self, img):
        return self.transform_modes[self.current_mode]["func"](img)
    
    def get_current_mode_name(self):
        return self.transform_modes[self.current_mode]["name"]
```

## 性能考虑

### 1. 变换顺序优化
```python
# 低效：多次变换
img = img.mirror().flip().rotation_corr(z_rotation=90)

# 高效：合并变换（如果可能）
# 先计算最终变换矩阵，然后一次性应用
```

### 2. 条件变换
```python
def conditional_transform(img, enable_mirror=False, enable_flip=False, rotation=0):
    """只在需要时进行变换，避免不必要的计算"""
    if enable_mirror:
        img = img.mirror()
    
    if enable_flip:
        img = img.flip()
    
    if rotation != 0:
        img = img.rotation_corr(z_rotation=rotation)
    
    return img
```

## 常见应用场景

### 1. 视频通话应用
```python
# 前置摄像头自拍效果
if camera_type == "front":
    img = img.mirror()  # 用户看到镜像效果
```

### 2. 监控系统
```python
# 根据摄像头安装角度自动校正
def auto_correct_surveillance_camera(img, installation_angle):
    correction_map = {
        0: lambda x: x,                                    # 正常安装
        90: lambda x: x.rotation_corr(z_rotation=270),     # 顺时针90°安装
        180: lambda x: x.rotation_corr(z_rotation=180),    # 倒装
        270: lambda x: x.rotation_corr(z_rotation=90)      # 逆时针90°安装
    }
    return correction_map.get(installation_angle, lambda x: x)(img)
```

### 3. 图像识别预处理
```python
def preprocess_for_recognition(img, normalize_orientation=True):
    """为图像识别准备标准化的图像"""
    if normalize_orientation:
        # 确保图像方向一致
        img = img.rotation_corr(z_rotation=0)  # 重置旋转
    
    return img
```

## 调试和测试

### 1. 可视化变换效果
```python
def visualize_transforms(img):
    """在一个画面中显示所有变换效果"""
    transforms = [
        ("原始", img),
        ("水平翻转", img.mirror()),
        ("垂直翻转", img.flip()),
        ("旋转90°", img.rotation_corr(z_rotation=90)),
        ("旋转180°", img.rotation_corr(z_rotation=180)),
        ("旋转270°", img.rotation_corr(z_rotation=270))
    ]
    
    # 创建组合显示（需要根据实际API调整）
    for i, (name, transformed_img) in enumerate(transforms):
        # 在不同位置显示各种变换效果
        pass
```

### 2. 性能测试
```python
def benchmark_transforms(img, iterations=100):
    """测试各种变换的性能"""
    import time
    
    transforms = [
        ("mirror", lambda x: x.mirror()),
        ("flip", lambda x: x.flip()),
        ("rotate_90", lambda x: x.rotation_corr(z_rotation=90)),
        ("rotate_180", lambda x: x.rotation_corr(z_rotation=180))
    ]
    
    for name, transform_func in transforms:
        start_time = time.ticks_ms()
        for _ in range(iterations):
            result = transform_func(img)
        end_time = time.ticks_ms()
        
        avg_time = (end_time - start_time) / iterations
        print(f"{name}: {avg_time:.2f}ms per transform")
```

## 注意事项

1. **内存使用**: 每次变换可能创建新的图像对象，注意内存管理
2. **变换顺序**: 不同的变换顺序可能产生不同的结果
3. **性能影响**: 频繁的图像变换会影响帧率
4. **坐标系统**: 变换后坐标系统可能发生变化，影响后续的图像处理

## 快速参考

| 功能 | 函数 | 效果 |
|------|------|------|
| 水平翻转 | `img.mirror()` | 左右镜像 |
| 垂直翻转 | `img.flip()` | 上下翻转 |
| 顺时针90° | `img.rotation_corr(z_rotation=90)` | 向右旋转90° |
| 逆时针90° | `img.rotation_corr(z_rotation=270)` | 向左旋转90° |
| 180°旋转 | `img.rotation_corr(z_rotation=180)` | 倒置 |
