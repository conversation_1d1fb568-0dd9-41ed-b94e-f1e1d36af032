# K230图像翻转快速参考

## 🚀 快速开始

### 1. 导入头文件
```python
from flip_utils import *
```

### 2. 在图像处理循环中调用
```python
while True:
    img = sensor.snapshot(chn=CAM_CHN_ID_0)
    
    # 选择一种翻转方式:
    img = mirror(img)        # 水平翻转
    # img = flip(img)        # 垂直翻转  
    # img = rot90(img)       # 顺时针90度
    # img = rot180(img)      # 180度旋转
    # img = rot270(img)      # 逆时针90度
    
    Display.show_image(img)
```

## 📋 函数速查表

| 函数名 | 效果 | 使用场景 |
|--------|------|----------|
| `mirror(img)` | 水平翻转（左右镜像） | 前置摄像头自拍 |
| `flip(img)` | 垂直翻转（上下翻转） | 倒装摄像头 |
| `rot90(img)` | 顺时针旋转90° | 右侧安装摄像头 |
| `rot180(img)` | 旋转180° | 完全倒置的摄像头 |
| `rot270(img)` | 逆时针旋转90° | 左侧安装摄像头 |
| `selfie_mode(img)` | 自拍模式 | 前置摄像头 |
| `fix_upside_down(img)` | 修正倒装 | 倒装摄像头 |

## 🎯 常用场景

### 前置摄像头自拍效果
```python
img = mirror(img)
# 或者
img = selfie_mode(img)
```

### 摄像头倒装
```python
img = rot180(img)
# 或者
img = fix_upside_down(img)
```

### 摄像头侧装
```python
# 右侧安装
img = rot90(img)

# 左侧安装  
img = rot270(img)
```

### 一键变换
```python
img = quick_transform(img, "mirror")    # 水平翻转
img = quick_transform(img, "rot90")     # 顺时针90度
img = quick_transform(img, "selfie")    # 自拍模式
```

### 自定义组合
```python
# 水平翻转 + 旋转90度
img = custom_transform(img, do_mirror=True, rotation=90)

# 垂直翻转 + 水平翻转
img = custom_transform(img, do_mirror=True, do_flip=True)
```

## 📁 文件说明

- **`flip_utils.py`** - 主要的翻转函数库（头文件）
- **`simple_flip_example.py`** - 最简单的使用示例
- **`main_with_transform.py`** - 完整的使用示例
- **`image_transform.py`** - 高级功能版本

## 🔧 修改您现有的代码

只需要在您现有代码中添加两行：

```python
# 1. 在文件顶部导入
from flip_utils import *

# 2. 在获取图像后添加翻转
img = sensor.snapshot(chn=CAM_CHN_ID_0)
img = mirror(img)  # 添加这一行进行翻转
```

## ⚡ 性能提示

- 翻转操作会消耗一定的计算资源
- 如果不需要翻转，注释掉翻转函数调用
- 避免多次重复翻转同一张图像
- 组合变换比多次单独变换更高效

## 🐛 常见问题

**Q: 翻转后图像显示异常？**
A: 检查是否多次调用了翻转函数

**Q: 性能下降？**
A: 确保只在需要时调用翻转函数，避免不必要的变换

**Q: 想要其他角度旋转？**
A: 使用 `img.rotation_corr(z_rotation=角度)` 进行自定义角度旋转
