import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

sensor = None

# 翻转模式设置
flip_modes = [
    {"name": "原始", "mirror": False, "flip": False, "rotation": 0},
    {"name": "水平翻转", "mirror": True, "flip": False, "rotation": 0},
    {"name": "垂直翻转", "mirror": False, "flip": True, "rotation": 0},
    {"name": "水平+垂直", "mirror": True, "flip": True, "rotation": 0},
    {"name": "旋转90°", "mirror": False, "flip": False, "rotation": 90},
    {"name": "旋转180°", "mirror": False, "flip": False, "rotation": 180},
    {"name": "旋转270°", "mirror": False, "flip": False, "rotation": 270},
    {"name": "镜像+旋转90°", "mirror": True, "flip": False, "rotation": 90}
]

current_mode = 0
last_switch_time = 0

try:
    print("K230 图像翻转演示")

    sensor = Sensor(width=640, height=640)
    sensor.reset()

    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.LT9611, to_ide=True)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    while True:
        clock.tick()
        os.exitpoint()
        
        # 每3秒自动切换翻转模式
        current_time = time.ticks_ms()
        if current_time - last_switch_time > 3000:  # 3秒
            current_mode = (current_mode + 1) % len(flip_modes)
            last_switch_time = current_time
            print(f"切换到模式: {flip_modes[current_mode]['name']}")

        # 获取原始图像
        img = sensor.snapshot(chn=CAM_CHN_ID_0)
        
        # 应用当前翻转模式
        mode = flip_modes[current_mode]
        
        # 水平翻转（左右镜像）
        if mode["mirror"]:
            img = img.mirror()
        
        # 垂直翻转（上下镜像）
        if mode["flip"]:
            img = img.flip()
        
        # 旋转
        if mode["rotation"] != 0:
            img = img.rotation_corr(x_rotation=0, y_rotation=0, z_rotation=mode["rotation"])

        # 在图像上绘制一些标识，方便观察翻转效果
        # 绘制箭头指向右上角
        img.draw_line(50, 100, 150, 50, color=(255, 0, 0), thickness=5)  # 箭头主体
        img.draw_line(150, 50, 130, 70, color=(255, 0, 0), thickness=3)  # 箭头头部1
        img.draw_line(150, 50, 130, 30, color=(255, 0, 0), thickness=3)  # 箭头头部2
        
        # 绘制文字标识
        img.draw_string_advanced(50, 50, 30, "TOP-RIGHT", color=(255, 255, 0))
        
        # 在四个角落绘制标识
        img.draw_circle(50, 50, 20, color=(255, 0, 0), thickness=3, fill=False)      # 左上
        img.draw_circle(590, 50, 20, color=(0, 255, 0), thickness=3, fill=False)     # 右上
        img.draw_circle(50, 590, 20, color=(0, 0, 255), thickness=3, fill=False)     # 左下
        img.draw_circle(590, 590, 20, color=(255, 0, 255), thickness=3, fill=False)  # 右下
        
        # 在中心绘制十字
        img.draw_line(320, 300, 320, 340, color=(255, 255, 255), thickness=3)  # 垂直线
        img.draw_line(300, 320, 340, 320, color=(255, 255, 255), thickness=3)  # 水平线

        # 显示当前模式信息
        mode_info = [
            f"模式: {mode['name']}",
            f"水平翻转: {'是' if mode['mirror'] else '否'}",
            f"垂直翻转: {'是' if mode['flip'] else '否'}",
            f"旋转角度: {mode['rotation']}°",
            f"FPS: {clock.fps():.1f}",
            f"下一个模式: {flip_modes[(current_mode + 1) % len(flip_modes)]['name']}"
        ]
        
        for i, info in enumerate(mode_info):
            img.draw_string_advanced(10, 200 + i * 30, 20, info, color=(0, 255, 255))

        # 显示函数调用示例
        function_calls = []
        if mode["mirror"]:
            function_calls.append("img.mirror()")
        if mode["flip"]:
            function_calls.append("img.flip()")
        if mode["rotation"] != 0:
            function_calls.append(f"img.rotation_corr(z_rotation={mode['rotation']})")
        
        if function_calls:
            img.draw_string_advanced(10, 400, 18, "函数调用:", color=(255, 255, 255))
            for i, call in enumerate(function_calls):
                img.draw_string_advanced(10, 425 + i * 25, 16, call, color=(255, 200, 100))
        else:
            img.draw_string_advanced(10, 400, 18, "无翻转处理", color=(255, 255, 255))

        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
